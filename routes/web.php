<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\HotelController;

// Default route - redirect to English home
Route::get('/', function () {
    return redirect()->route('home', ['locale' => 'en']);
});

// Multilingual routes
Route::group(['prefix' => '{locale}', 'where' => ['locale' => 'en|ar']], function () {
    // Home routes
    Route::get('/', [HomeController::class, 'index'])->name('home');

    // Hotel routes
    Route::get('/{slug}', [HotelController::class, 'show'])
        ->where('slug', '[a-z0-9-]+')
        ->name('hotel.show');
});
