@use '../settings/colors' as *;
@use '../settings/spacing' as *;
@use '../settings/breakpoints' as *;
@use '../tools/mixins' as *;

.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: $color-leisure-700;
  padding: $spacing-lg $spacing-md;
  color: $color-white;

  &__left {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    &-menuIcon {
      margin-right: 10px;
    }

    &-logoMobile {
      display: block;
    }

    &-logoDesktop {
      display: none;
    }
  }

  &__right {
    display: flex;
    align-items: center;
    gap: $spacing-4xl;

    &-link {
      display: none;
      color: $color-white;
      text-decoration: none;
      align-items: center;
      gap: 8px;

      &:hover {
        text-decoration: underline;
      }

      img {
        width: 20px;
        height: 20px;
      }
    }

    &-utilities {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      &-language {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
      }

      &-currency {
        display: flex;
        align-items: center;
        gap: $spacing-xs;

        .icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  @include mq-lg {
    padding: $spacing-sm $spacing-4xl;
    &__left {
      &-menuIcon {
        display: none;
      }

      &-logoMobile {
        display: none;
      }

      &-logoDesktop {
        display: block;
      }
    }

    &__right {
      &-link {
        display: flex;
      }
    }
  }
}