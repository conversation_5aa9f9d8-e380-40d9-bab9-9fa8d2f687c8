@use '../settings/spacing' as *;
@use '../settings/breakpoints' as *;
@use '../settings/colors' as *;
@use '../tools/mixins' as *;

.home-container {
  padding: $spacing-lg;
  max-width: 100%;
  margin: 0;
}

.home-layout {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;

  @include mq-md {
    flex-direction: row;
    gap: 0;
    height: 100vh;
  }
}

.venue-cards-section {
  flex: 1;
  padding: $spacing-lg;
  overflow-y: auto;

  @include mq-md {
    max-height: 100vh;
  }
}

.venue-cards-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-lg;

  @include mq-md {
    grid-template-columns: 1fr;
    gap: $spacing-xl;
  }

  @include mq-lg {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  }
}

.map-section {
  @include mq-md {
    width: 50%;
    margin-right: 0;
    margin-top: 0;
  }
}

.map-placeholder {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
  border: 2px dashed $color-neutral-300;
  border-radius: $spacing-sm;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 300px;

  @include mq-md {
    height: 801px;
    border-radius: 0;
    border: none;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  }

  &::before {
    content: "Map Coming Soon";
    font-size: $spacing-lg;
    color: $color-neutral-500;
    font-weight: 500;

    @include mq-md {
      font-size: $spacing-xl;
    }
  }
}