@extends('layouts.app')

@section('title', $hotel['name'] . ' - ' . config('app.name'))
@section('description', $hotel['description'])


@section('content')
<article class="detailsPage">
    <section class="detailsPage__topSection">
        <header class="detailsPage__topSection-header">
            <h1 class="detailsPage__topSection-header-title heading-sm">{{ $hotel['name'] }}</h1>
            <h2 class="detailsPage__topSection-header-subtitle text-md">{{ $hotel['address'] }}</h2>
            <section class="detailsPage__topSection-header-tags fgTags">
                @foreach($hotel['tags'] as $tag)
                <span class="fgTag">{{ $tag }}</span>
                @endforeach
            </section>
        </header>
        <div class="detailsPage__topSection-utilities">
            <div class="detailsPage__topSection-utilities-pricing">
                <span class="detailsPage__topSection-utilities-pricing-from">from</span>
                <span class="detailsPage__topSection-utilities-pricing-value heading-md">{{ $hotel['price']['amount']." ".$hotel['price']['currency'] }}</span>
                <span class="detailsPage__topSection-utilities-pricing-night">/ night</span>
            </div>
            <div class="detailsPage__topSection-utilities-note text-xs">incl. taxes and fees</div>
            <section class="detailsPage__topSection-utilities-ctas">
                <button class="detailsPage__topSection-utilities-ctas-share fgBtn fgBtn--secondary fgBtn--small fgBtn--icon fgBtn--left">
                    Share
                    <x-svg-icon name="share" />
                </button>
                <button class="detailsPage__topSection-utilities-ctas-save fgBtn fgBtn--secondary fgBtn--small fgBtn--icon fgBtn--left">
                    Save
                    <x-svg-icon name="save" />
                </button>
                <button class="detailsPage__topSection-utilities-ctas-book fgBtn fgBtn--primary fgBtn--small">
                    Book your stay
                </button>
            </section>
        </div>
    </section>

    <section class="detailsPage__gallery">
        <div class="gallery">
            <div class="gallery__item gallery--left gallery-trigger" data-index="0">
                <img class="gallery__img" src="https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hotel main view">
            </div>
            <div class="gallery__item gallery--top gallery-trigger" data-index="1">
                <img class="gallery__img" src="https://images.unsplash.com/photo-1684398863223-1a517d708ed5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Luxury hotel room">
            </div>
            <div class="gallery__item gallery--bottom-a gallery-trigger" data-index="2">
                <img class="gallery__img" src="https://images.unsplash.com/photo-1577032933291-977076dab751?q=80&w=2063&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hotel lobby">
            </div>
            <div class="gallery__item gallery--bottom-b gallery-trigger" data-index="3">
                <img class="gallery__img" src="https://images.unsplash.com/photo-1662050196100-6f8afc83d585?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Hotel amenities">
                <span class="gallery--overlay"></span>
                <span class="gallery--caption">+{{ count($hotel['gallery']) }}&nbsp;Photos</span>
            </div>
        </div>
    </section>


    <section class="detailsPage__details">
        <section class="detailsPage__details-left">
            <article class="detailsPageContentBlock detailsPageContentBlock--borderedContentVariant">
                <h2 class="detailsPageContentBlock__title">Property Highlights</h2>
                <section class="detailsPageContentBlock__content">
                    <x-grid :items="[
                [
                    'icon' => Vite::asset('resources/images/svg/cocktail.svg'),
                    'title' => 'Grand Lounge',
                    'description' => 'A place to relax and enjoy a drink or a meal'
                ],
                [
                    'icon' => Vite::asset('resources/images/svg/housekeeping.svg'),
                    'title' => 'Housekeeping', 
                    'description' => 'Clean and tidy rooms'
                ],
                [
                    'icon' => Vite::asset('resources/images/svg/checkmark.svg'),
                    'title' => 'Business Facilities',
                    'description' => 'Meeting rooms and event spaces'
                ],
                [
                    'icon' => Vite::asset('resources/images/svg/pool.svg'),
                    'title' => 'Rooftop Pool & Bar',
                    'description' => 'Outdoor infinity pool with stunning city views'
                ],
                [
                    'icon' => Vite::asset('resources/images/svg/wellness.svg'),
                    'title' => 'Wellness & Leisure',
                    'description' => 'Full-service spa with relaxation treatments'
                ]
            ]" class="propertyHighlightsGrid" />
                </section>
            </article>

            <article class="detailsPageContentBlock">
                <h2 class="detailsPageContentBlock__title">Property Description</h2>
                <section class="detailsPageContentBlock__content">
                    <p class="text-sm">Andaz Doha by Hyatt is a luxury lifestyle hotel located in West Bay, Doha's prestigious district. The hotel offers 256 rooms, including 32 suites and 4 Royal Suites, along with 56 residences designed for extended stays. Each accommodation features eco-friendly amenities, locally inspired design elements, and offers sea or city views.
                    </p>
                    <p class="text-sm mt-xl">
                        Guests can indulge in diverse culinary experiences at The Salt Road Doha, with upcoming venues like Mr. & Mrs. Hawker and Mumble Jungle set to enhance the dining scene. Wellness facilities include the Alara Spa, inspired by traditional Hammam rituals, a 24-hour fitness center, and an outdoor pool. For business and events, the hotel provides 1,600 square meters of versatile space, including a 750-square-meter ballroom and culturally themed studios.</p>
                </section>
            </article>
            <article class="detailsPageContentBlock">
                <h2 class="detailsPageContentBlock__title">Facilities</h2>
                <section class="detailsPageContentBlock__content">
                    <div class="facilitiesList">
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/airplane.svg') }}" alt="Airport transportation">
                            <p class="facilitiesList__item-text">Airport transportation (extra-charge)</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/dining.svg') }}" alt="On-site dining">
                            <p class="facilitiesList__item-text">On-site dining options</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/user.svg') }}" alt="Concierge">
                            <p class="facilitiesList__item-text">Concierge services</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/laundry.svg') }}" alt="Laundry">
                            <p class="facilitiesList__item-text">Dry cleaning / laundry service</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/wifi.svg') }}" alt="WiFi">
                            <p class="facilitiesList__item-text">Free Wi-Fi in public areas</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/spa.svg') }}" alt="Spa">
                            <p class="facilitiesList__item-text">Spa and wellness facilities</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/wellness.svg') }}" alt="Fitness">
                            <p class="facilitiesList__item-text">Fitness center access</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/checkmark.svg') }}" alt="Room service">
                            <p class="facilitiesList__item-text">Room service (24 hours)</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/checkmark.svg') }}" alt="Valet">
                            <p class="facilitiesList__item-text">Valet parking service</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/business.svg') }}" alt="Meeting">
                            <p class="facilitiesList__item-text">Meeting and event space availability</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/dining.svg') }}" alt="Breakfast">
                            <p class="facilitiesList__item-text">Complimentary breakfast</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/business.svg') }}" alt="Business">
                            <p class="facilitiesList__item-text">Business center services</p>
                        </div>
                    </div>
                </section>
            </article>
        </section>
        <section class="detailsPage__details-right">
            <article class="detailsPageContentBlock detailsPageContentBlock--borderedContentVariant">
                <h2 class="detailsPageContentBlock__title">Location Details</h2>
                <section class="detailsPageContentBlock__content">
                    <div class="locationCard">
                        <img class="locationCard__image" src="{{ Vite::asset('resources/images/map.png') }}" alt="map">
                        <section class="locationCard__content">
                            <h3 class="locationCard__title">Diplomatic Street 23, Zone 61, Doha</h3>
                            <a href="#" class="locationCard__link">View on maps</a>
                            <div class="locationCard__item">
                                <img class="locationCard__item-icon" src="{{ Vite::asset('resources/images/svg/museum.svg') }}" alt="Museum">
                                <p class="locationCard__item-text">Museum of illusions
                                    <span class="locationCard__item-distance">1.2 km</span>
                                </p>
                            </div>
                            <div class="locationCard__item">
                                <img class="locationCard__item-icon" src="{{ Vite::asset('resources/images/svg/shopping.svg') }}" alt="Shopping">
                                <p class="locationCard__item-text">City Center Doha Shopping Mall
                                    <span class="locationCard__item-distance">1.2 km</span>
                                </p>
                            </div>
                            <div class="locationCard__item">
                                <img class="locationCard__item-icon" src="{{ Vite::asset('resources/images/svg/park.svg') }}" alt="Park">
                                <p class="locationCard__item-text">Doha Park
                                    <span class="locationCard__item-distance">2.3 km</span>
                                </p>
                            </div>
                        </section>
                    </div>
                </section>
            </article>
            <article class="detailsPageContentBlock detailsPageContentBlock--borderedContentVariant">
                <h2 class="detailsPageContentBlock__title">Property Policies</h2>
                <section class="detailsPageContentBlock__content">
                    <article class="hotelPoliciesCard">
                        <span class="text-sm">This is the sidebar content area for additional information.</span>
                    </article>
                </section>
            </article>
        </section>
    </section>

</article>
@endsection

@push('styles')
<!-- GLightbox CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css" />
<style>
.gallery-trigger {
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-trigger:hover {
    transform: scale(1.02);
}

.gallery-trigger:hover .gallery__img {
    filter: brightness(1.1);
}
</style>
@endpush

@push('scripts')
<!-- GLightbox JS -->
<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gallery images data - using the same Unsplash images with more variety
    const galleryImages = [
        {
            url: 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            alt: 'Hotel main view'
        },
        {
            url: 'https://images.unsplash.com/photo-1684398863223-1a517d708ed5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            alt: 'Luxury hotel room'
        },
        {
            url: 'https://images.unsplash.com/photo-1577032933291-977076dab751?q=80&w=2063&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            alt: 'Hotel lobby'
        },
        {
            url: 'https://images.unsplash.com/photo-1662050196100-6f8afc83d585?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            alt: 'Hotel amenities'
        },
        {
            url: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop',
            alt: 'Hotel exterior'
        },
        {
            url: 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=800&h=600&fit=crop',
            alt: 'Hotel restaurant'
        },
        {
            url: 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=800&h=600&fit=crop',
            alt: 'Hotel spa'
        },
        {
            url: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop',
            alt: 'Hotel pool'
        },
        {
            url: 'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&h=600&fit=crop',
            alt: 'Hotel bar'
        },
        {
            url: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&h=600&fit=crop',
            alt: 'Hotel conference room'
        },
        {
            url: 'https://images.unsplash.com/photo-1596394516093-501ba68a0ba6?w=800&h=600&fit=crop',
            alt: 'Hotel gym'
        },
        {
            url: 'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?w=800&h=600&fit=crop',
            alt: 'Hotel terrace'
        }
    ];

    let lightbox = null;

    // Initialize GLightbox
    const initLightbox = () => {
        lightbox = GLightbox({
            touchNavigation: true,
            loop: true,
            autoplayVideos: false,
            closeButton: true,
            zoomable: true,
            draggable: true
        });

        // Set gallery images
        lightbox.setElements(galleryImages.map(image => ({
            href: image.url,
            type: 'image',
            alt: image.alt
        })));
    };

    // Open gallery function
    function openGallery(index = 0) {
        if (lightbox) {
            lightbox.openAt(index);
        }
    }

    // Add click handlers to gallery items
    const galleryTriggers = document.querySelectorAll('.gallery-trigger');
    galleryTriggers.forEach((trigger) => {
        trigger.addEventListener('click', (e) => {
            e.preventDefault();
            const index = parseInt(trigger.dataset.index) || 0;
            openGallery(index);
        });

        // Add cursor pointer style
        trigger.style.cursor = 'pointer';
    });

    // Initialize lightbox
    initLightbox();
});
</script>
@endpush