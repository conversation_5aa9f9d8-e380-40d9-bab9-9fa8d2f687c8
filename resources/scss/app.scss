// ==========================================================================
// ITCSS Structure
// ==========================================================================

// 1. Settings - Global variables, config switches
@use 'settings/colors';
@use 'settings/typography';
@use 'settings/spacing';
@use 'settings/breakpoints';
@use 'settings/text-sizes';

// 2. Tools - Mixins and functions
@use 'tools/mixins';
@use 'tools/functions';

// 3. Generic - Ground-zero styles (normalize, reset, box-sizing)
@use 'generic/normalize';
@use 'generic/reset';
@use 'generic/box-sizing';

// 4. Elements - Unclassed HTML elements
@use 'elements/page';
@use 'elements/headings';
@use 'elements/links';
@use 'elements/lists';
@use 'elements/images';
@use 'elements/body';

// 5. Objects - Cosmetic-free design patterns
@use 'objects/wrapper';
@use 'objects/layout';
@use 'objects/media';

// 6. Components - Designed components, chunks of UI
@use 'components/header';
@use 'components/footer';
@use 'components/navigation';
@use 'components/buttons';
@use 'components/cards';
@use 'components/inputs';
@use 'components/tags';
@use 'components/details-page';

// 7. Utilities - Helpers and overrides
@use 'utilities/spacing' as spacing2;
@use 'utilities/text';
@use 'utilities/display';

// Fonts - Google Fonts (Noto Kufi Arabic)
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Dancing+Script:wght@400..700&family=Lusitana:wght@400;700&family=Noto+Kufi+Arabic:wght@100..900&display=swap');
