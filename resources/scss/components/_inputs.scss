@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/spacing' as *;
@use '../settings/breakpoints' as *;
@use '../settings/radius' as *;
@use '../tools/mixins' as *;

.fgInput {
  font-family: $font-family-primary;
  position: relative;
  display: inline-block;
  min-width: 25rem;

  .input {
    width: 100%;
    max-width: 25rem;
    background: $color-white;
    border: 1px solid $color-neutral-300;
    border-radius: $radius-md;
    font-size: $font-size-base;
    line-height: 1.5;
    color: $color-neutral-800;
    padding: calc($spacing-md - 1px) $spacing-md;
    transition: border-color 0.2s,   box-shadow 0.2s;

    &::placeholder {
      color: $color-neutral-500;
      opacity: 1;
    }

    &:hover:not(:disabled):not(.input--error):not(:focus) {
      border-color: $color-neutral-600;
    }

    &:focus {
      border: 2px solid $color-neutral-600;
      padding: calc($spacing-md - 2px) $spacing-md;
    }

    &--error {
      border-color: $color-danger-600;
      color: $color-neutral-800;
    }

    &:disabled,
    &--disabled {
      background: $color-neutral-050;
      color: $color-neutral-300;
      border-color: $color-neutral-100;
      cursor: not-allowed;

      &::placeholder {
        color: $color-neutral-300;
      }
    }
  }

  .inputIcon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    display: flex;
    align-items: center;

    svg {
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  .input-label {
    font-size: $font-size-sm;
    color: $color-neutral-600;
    margin-bottom: $spacing-xs;
    display: block;
  }

  .message {
    font-size: $font-size-sm;
    margin-top: $spacing-xs;

    &--error {
      color: $color-danger-600;
    }
  }

  &--withIconLeft {
    .input {
      padding-left: calc(#{$spacing-md} + $spacing-xl + $spacing-xxs);

      &:focus {
        padding-left: calc(#{$spacing-md} + $spacing-xl + $spacing-xxs);
      }
    }

    .inputIcon {
      left: $spacing-md;
    }
  }

  &--withIconRight {
    .input {
      padding-right: calc(#{$spacing-md} + $spacing-xl + $spacing-xxs);

      &:focus {
        padding-right: calc(#{$spacing-md} + $spacing-xl + $spacing-xxs);
      }
    }
    
    .inputIcon {
      right: $spacing-md;
    }
  }
}

.textarea {
    display: flex;
    height: 113px;
    width: 100%;
    padding: calc($spacing-md - 1px) $spacing-md;
    align-items: flex-start;
    border-radius: $radius-md;
    border: 1px solid $color-neutral-300;
}