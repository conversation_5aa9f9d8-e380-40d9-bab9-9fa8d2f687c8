@use '../settings/breakpoints' as *;
@use '../settings/typography' as *;
@use '../settings/spacing' as *;
@use '../settings/colors' as *;
@use '../settings/headings' as *;
@use '../settings/text-sizes' as *;

// Media Queries 
@mixin mq-md {
  @media (min-width: $breakpoint-md) {
    @content;
  }
}

@mixin mq-sm {
  @media (min-width: $breakpoint-sm) {
    @content;
  }
}

@mixin mq-lg {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

// @TODO - check and remove if necessary
@mixin mq-tablet {
  @media (min-width: 481px) and (max-width: 768px) {
    @content;
  }
}

@mixin not-selectable {
  -webkit-touch-callout: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Safari */
  -khtml-user-select: none;
  /* Konqueror HTML */
  -moz-user-select: none;
  /* Old versions of Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  /* Non-prefixed version, currently
    supported by Chrome, Edge, Opera and Firefox */
}

// Buttons
@mixin btn-disabled {
  @include not-selectable;
  cursor: not-allowed;
  pointer-events: none;
}

@mixin text-class($size) {
  $token   : map-get(map-get($text-sizes,$size), var);
  $fallback: map-get(map-get($text-sizes,$size), fallback);

  .text-#{$size} {
    
    // color      : $color-black;
    font-family: $font-family-primary;
    font-style : normal;
    font-weight: $font-weight-normal;                           
    font-size  : var(#{$token}, #{$fallback});
  }

  /* muted (grey) variant */
  // .text-#{$size}--muted {
  //   color: $color-neutral-600;
  // }

  // /* strong (semi-bold) variant */
  // .text-#{$size}--strong {
  //   font-weight: $font-weight-bold;
  // }
}

// Headings
@mixin heading-class($size) {
  $mobile  : map-get(map-get($heading-sizes, $size), mobile);
  $desktop : map-get(map-get($heading-sizes, $size), desktop);

  .heading-#{$size} {
    font-family: $font-family-primary;
    font-weight: $font-weight-medium;
    letter-spacing: 0;
    line-height: 1.1;
    color: $color-black;
    font-size: $mobile;

    @include mq-md { font-size: $desktop; }
  }

  /* secondary modifier */
  .heading-#{$size}--secondary {
    font-family: $font-family-secondary;
    font-weight: $font-weight-semibold;
    text-transform: uppercase;
  }
}
