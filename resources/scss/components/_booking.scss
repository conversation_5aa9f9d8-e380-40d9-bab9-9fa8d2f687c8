@use '../settings/spacing' as *;
@use '../settings/breakpoints' as *;
@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../tools/mixins' as *;

.booking-container {
  min-height: 100vh;
  padding: $spacing-lg;
}

.booking-layout {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  @include mq-lg {
    flex-direction: row;
  }
}

.booking-form-section {
  border: 1px solid $color-neutral-200;
  border-radius: $spacing-xs;
  flex: 1;
  padding: $spacing-xl;
  margin-right: $spacing-lg;
  
  @include mq-lg {
    padding: $spacing-2xl;
  }
}

.booking-header {
  margin-bottom: $spacing-xl;
}

.booking-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $color-black;
  margin-bottom: $spacing-md;
}

.booking-auth-notice {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  color: $color-neutral-600;
  font-size: $font-size-sm;
  
  svg {
    color: #6366f1;
    flex-shrink: 0;
  }
  
  strong {
    color: #6366f1;
  }
}

.room-details {
  margin-bottom: $spacing-2xl;
  padding-bottom: $spacing-xl;
  border-bottom: 1px solid $color-neutral-200;
}

.room-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $color-black;
  margin-bottom: $spacing-xs;
}

.room-description {
  color: $color-neutral-600;
  font-size: $font-size-sm;
  margin-bottom: $spacing-lg;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-md;
  margin-bottom: $spacing-md;
  
  @include mq-md {
    grid-template-columns: 1fr 1fr;
  }
}

.form-group {
  position: relative;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: $spacing-md;
  border: 1px solid $color-neutral-300;
  border-radius: $spacing-xs;
  font-size: $font-size-base;
  transition: border-color 0.2s;
  
  &:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  
  &.valid {
    border-color: #10b981;
    background-color: #f0fdf4;
  }
  
  &.error {
    border-color: #ef4444;
    background-color: #fef2f2;
  }
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.input-icon {
  position: absolute;
  right: $spacing-md;
  top: 50%;
  transform: translateY(-50%);
  
  &.success {
    color: #10b981;
  }
}

.error-message {
  color: #ef4444;
  font-size: $font-size-xs;
  margin-top: $spacing-xs;
}

.info-sections {
  margin-top: $spacing-2xl;
}

.info-section {
  background: $color-neutral-050;
  padding: $spacing-lg;
  border-radius: $spacing-xs;
  margin-bottom: $spacing-lg;
}

.info-header {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;
  
  svg {
    color: $color-neutral-500;
    flex-shrink: 0;
  }
  
  h3 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $color-black;
    margin: 0;
  }
}

.info-section p {
  color: $color-neutral-600;
  font-size: $font-size-sm;
  line-height: 1.5;
  margin-bottom: $spacing-sm;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.payment-methods {
  display: flex;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.payment-icon {
  padding: $spacing-xs $spacing-sm;
  border: 1px solid $color-neutral-300;
  border-radius: $spacing-xs;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  background: white;
  
  &.google-pay {
    background: #4285f4;
    color: white;
    border-color: #4285f4;
  }
  
  &.apple-pay {
    background: #000;
    color: white;
    border-color: #000;
  }
  
  &.mastercard {
    background: #eb001b;
    color: white;
    border-color: #eb001b;
  }
  
  &.visa {
    background: #1a1f71;
    color: white;
    border-color: #1a1f71;
  }
  
  &.amex {
    background: #006fcf;
    color: white;
    border-color: #006fcf;
  }
}

.booking-summary-section {
  padding: $spacing-xl;
  border: 1px solid $color-neutral-200;
  border-radius: $spacing-xs;
  
  @include mq-lg {
    width: 400px;
    padding: $spacing-2xl;
  }
}

.booking-summary {
  position: sticky;
  top: $spacing-lg;
}

.summary-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $color-black;
  margin-bottom: $spacing-lg;
}

.hotel-info {
  display: flex;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-lg;
  border-bottom: 1px solid $color-neutral-200;
}

.hotel-image {
  width: 80px;
  height: 80px;
  border-radius: $spacing-xs;
  object-fit: cover;
  flex-shrink: 0;
}

.hotel-details {
  flex: 1;
}

.hotel-name {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $color-black;
  margin-bottom: $spacing-xs;
}

.hotel-location {
  color: $color-neutral-600;
  font-size: $font-size-sm;
  margin-bottom: $spacing-sm;
}

.hotel-dates {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $color-neutral-600;
  font-size: $font-size-sm;
  
  svg {
    flex-shrink: 0;
  }
}

.room-summary {
  margin-bottom: $spacing-lg;
}

.room-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-md;
  padding-bottom: $spacing-md;
  border-bottom: 1px solid $color-neutral-200;
  
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.room-info {
  flex: 1;
  
  h4 {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $color-black;
    margin-bottom: $spacing-xs;
  }
  
  p {
    color: $color-neutral-600;
    font-size: $font-size-xs;
  }
}

.room-price {
  text-align: right;
  
  .nights {
    font-size: $font-size-sm;
    color: $color-neutral-600;
  }
  
  .price {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $color-black;
    margin: 0 $spacing-xs;
  }
  
  .per-night {
    font-size: $font-size-xs;
    color: $color-neutral-600;
  }
}

.price-summary {
  background: white;
  border-radius: $spacing-xs;
  margin-bottom: $spacing-lg;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-sm;
  
  &.total {
    margin-bottom: 0;
    padding-top: $spacing-sm;
    border-top: 1px solid $color-neutral-200;
    font-weight: $font-weight-semibold;
  }
}

.total-price {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $color-black;
}

.confirm-button {
  width: 100%;
  background: $color-leisure-700;
  color: white;
  border: none;
  padding: $spacing-sm;
  border-radius: $spacing-xs;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  cursor: pointer;
  
  &:hover {
    background: #5b21b6;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
  }
}
