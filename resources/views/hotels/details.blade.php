@extends('layouts.app')

@section('title', $hotel['name'] . ' - ' . config('app.name'))
@section('description', $hotel['description'])


@section('content')
<article class="detailsPage">
    <section class="detailsPage__topSection">
        <header class="detailsPage__topSection-header">
            <h1 class="detailsPage__topSection-header-title heading-sm">{{ $hotel['name'] }}</h1>
            <h2 class="detailsPage__topSection-header-subtitle text-md">{{ $hotel['address'] }}</h2>
            <section class="detailsPage__topSection-header-tags fgTags">
                @foreach($hotel['tags'] as $tag)
                <span class="fgTag">{{ $tag }}</span>
                @endforeach
            </section>
        </header>
        <div class="detailsPage__topSection-utilities">
            <div class="detailsPage__topSection-utilities-pricing">
                <span class="detailsPage__topSection-utilities-pricing-from">from</span>
                <span class="detailsPage__topSection-utilities-pricing-value heading-md">{{ $hotel['price']['amount']." ".$hotel['price']['currency'] }}</span>
                <span class="detailsPage__topSection-utilities-pricing-night">/ night</span>
            </div>
            <div class="detailsPage__topSection-utilities-note text-xs">incl. taxes and fees</div>
            <section class="detailsPage__topSection-utilities-ctas">
                <button class="detailsPage__topSection-utilities-ctas-share fgBtn fgBtn--secondary fgBtn--small fgBtn--icon fgBtn--left">
                    Share
                    <x-svg-icon name="share" />
                </button>
                <button class="detailsPage__topSection-utilities-ctas-save fgBtn fgBtn--secondary fgBtn--small fgBtn--icon fgBtn--left">
                    Save
                    <x-svg-icon name="save" />
                </button>
                <button class="detailsPage__topSection-utilities-ctas-book fgBtn fgBtn--primary fgBtn--small">
                    Book your stay
                </button>
            </section>
        </div>
    </section>

    <section class="detailsPage__gallery">
        <div class="gallery">
            <div class="gallery__item gallery--left">
                <img class="gallery__img" src="{{ asset($hotel['gallery'][0]) }}" alt="main">
            </div>
            <div class="gallery__item gallery--top">
                <img class="gallery__img" src="{{ asset($hotel['gallery'][1]) }}" alt="top">
            </div>
            <div class="gallery__item gallery--bottom-a">
                <img class="gallery__img" src="{{ asset($hotel['gallery'][2]) }}" alt="bottom">
            </div>
            <div class="gallery__item gallery--bottom-b">
                <img class="gallery__img" src="{{ asset($hotel['gallery'][3]) }}" alt="bottom">
                <span class="gallery--overlay"></span>
                <span class="gallery--caption">+{{ count($hotel['gallery']) }}&nbsp;Photos</span>
            </div>
        </div>
    </section>


    <section class="detailsPage__details">
        <section class="detailsPage__details-left">
            <article class="detailsPageContentBlock detailsPageContentBlock--borderedContentVariant">
                <h2 class="detailsPageContentBlock__title">Property Highlights</h2>
                <section class="detailsPageContentBlock__content">
                    <x-grid :items="[
                [
                    'icon' => Vite::asset('resources/images/svg/cocktail.svg'),
                    'title' => 'Grand Lounge',
                    'description' => 'A place to relax and enjoy a drink or a meal'
                ],
                [
                    'icon' => Vite::asset('resources/images/svg/housekeeping.svg'),
                    'title' => 'Housekeeping', 
                    'description' => 'Clean and tidy rooms'
                ],
                [
                    'icon' => Vite::asset('resources/images/svg/checkmark.svg'),
                    'title' => 'Business Facilities',
                    'description' => 'Meeting rooms and event spaces'
                ],
                [
                    'icon' => Vite::asset('resources/images/svg/pool.svg'),
                    'title' => 'Rooftop Pool & Bar',
                    'description' => 'Outdoor infinity pool with stunning city views'
                ],
                [
                    'icon' => Vite::asset('resources/images/svg/wellness.svg'),
                    'title' => 'Wellness & Leisure',
                    'description' => 'Full-service spa with relaxation treatments'
                ]
            ]" class="propertyHighlightsGrid" />
                </section>
            </article>

            <article class="detailsPageContentBlock">
                <h2 class="detailsPageContentBlock__title">Property Description</h2>
                <section class="detailsPageContentBlock__content">
                    <p class="text-sm">Andaz Doha by Hyatt is a luxury lifestyle hotel located in West Bay, Doha's prestigious district. The hotel offers 256 rooms, including 32 suites and 4 Royal Suites, along with 56 residences designed for extended stays. Each accommodation features eco-friendly amenities, locally inspired design elements, and offers sea or city views.
                    </p>
                    <p class="text-sm mt-xl">
                        Guests can indulge in diverse culinary experiences at The Salt Road Doha, with upcoming venues like Mr. & Mrs. Hawker and Mumble Jungle set to enhance the dining scene. Wellness facilities include the Alara Spa, inspired by traditional Hammam rituals, a 24-hour fitness center, and an outdoor pool. For business and events, the hotel provides 1,600 square meters of versatile space, including a 750-square-meter ballroom and culturally themed studios.</p>
                </section>
            </article>
            <article class="detailsPageContentBlock">
                <h2 class="detailsPageContentBlock__title">Facilities</h2>
                <section class="detailsPageContentBlock__content">
                    <div class="facilitiesList">
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/airplane.svg') }}" alt="Airport transportation">
                            <p class="facilitiesList__item-text">Airport transportation (extra-charge)</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/dining.svg') }}" alt="On-site dining">
                            <p class="facilitiesList__item-text">On-site dining options</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/user.svg') }}" alt="Concierge">
                            <p class="facilitiesList__item-text">Concierge services</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/laundry.svg') }}" alt="Laundry">
                            <p class="facilitiesList__item-text">Dry cleaning / laundry service</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/wifi.svg') }}" alt="WiFi">
                            <p class="facilitiesList__item-text">Free Wi-Fi in public areas</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/spa.svg') }}" alt="Spa">
                            <p class="facilitiesList__item-text">Spa and wellness facilities</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/wellness.svg') }}" alt="Fitness">
                            <p class="facilitiesList__item-text">Fitness center access</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/checkmark.svg') }}" alt="Room service">
                            <p class="facilitiesList__item-text">Room service (24 hours)</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/checkmark.svg') }}" alt="Valet">
                            <p class="facilitiesList__item-text">Valet parking service</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/business.svg') }}" alt="Meeting">
                            <p class="facilitiesList__item-text">Meeting and event space availability</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/dining.svg') }}" alt="Breakfast">
                            <p class="facilitiesList__item-text">Complimentary breakfast</p>
                        </div>
                        <div class="facilitiesList__item">
                            <img class="facilitiesList__item-icon" src="{{ Vite::asset('resources/images/svg/business.svg') }}" alt="Business">
                            <p class="facilitiesList__item-text">Business center services</p>
                        </div>
                    </div>
                </section>
            </article>
        </section>
        <section class="detailsPage__details-right">
            <article class="detailsPageContentBlock detailsPageContentBlock--borderedContentVariant">
                <h2 class="detailsPageContentBlock__title">Location Details</h2>
                <section class="detailsPageContentBlock__content">
                    <div class="locationCard">
                        <img class="locationCard__image" src="{{ Vite::asset('resources/images/map.png') }}" alt="map">
                        <section class="locationCard__content">
                            <h3 class="locationCard__title">Diplomatic Street 23, Zone 61, Doha</h3>
                            <a href="#" class="locationCard__link">View on maps</a>
                            <div class="locationCard__item">
                                <img class="locationCard__item-icon" src="{{ Vite::asset('resources/images/svg/museum.svg') }}" alt="Museum">
                                <p class="locationCard__item-text">Museum of illusions
                                    <span class="locationCard__item-distance">1.2 km</span>
                                </p>
                            </div>
                            <div class="locationCard__item">
                                <img class="locationCard__item-icon" src="{{ Vite::asset('resources/images/svg/shopping.svg') }}" alt="Shopping">
                                <p class="locationCard__item-text">City Center Doha Shopping Mall
                                    <span class="locationCard__item-distance">1.2 km</span>
                                </p>
                            </div>
                            <div class="locationCard__item">
                                <img class="locationCard__item-icon" src="{{ Vite::asset('resources/images/svg/park.svg') }}" alt="Park">
                                <p class="locationCard__item-text">Doha Park
                                    <span class="locationCard__item-distance">2.3 km</span>
                                </p>
                            </div>
                        </section>
                    </div>
                </section>
            </article>
            <article class="detailsPageContentBlock detailsPageContentBlock--borderedContentVariant">
                <h2 class="detailsPageContentBlock__title">Property Policies</h2>
                <section class="detailsPageContentBlock__content">
                    <article class="hotelPoliciesCard">
                        <span class="text-sm">This is the sidebar content area for additional information.</span>
                    </article>
                </section>
            </article>
        </section>
    </section>

</article>
@endsection