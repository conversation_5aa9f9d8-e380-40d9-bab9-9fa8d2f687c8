<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        $locale = $request->route('locale', 'en');

        app()->setLocale($locale);

        return view('booking.index', [
            'locale' => $locale,
            'bookingData' => $this->getBookingData(),
        ]);
    }
    
  
    private function getBookingData()
    {
        return [
            'hotel' => [
                'name' => 'Andaz Hotel, by Hyatt',
                'location' => 'Diplomatic Street 23, Zone 61, Doha',
                'image' => 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop',
                'dates' => '30 Aug - 03 Sep'
            ],
            'rooms' => [
                [
                    'id' => 1,
                    'type' => '1 King Bed Deluxe',
                    'description' => 'Room only (non-refundable)',
                    'price' => 720,
                    'currency' => 'QAR',
                    'nights' => 1
                ],
                [
                    'id' => 2,
                    'type' => '1 King Bed Sea View',
                    'description' => 'Bed & breakfast (flexible rates)',
                    'price' => 1020,
                    'currency' => 'QAR',
                    'nights' => 2
                ]
            ],
            'total' => [
                'rooms_selected' => 3,
                'total_price' => 8280,
                'currency' => 'QAR',
                'nights' => 4
            ],
            'payment_methods' => [
                'google_pay',
                'apple_pay',
                'mastercard',
                'visa',
                'amex'
            ]
        ];
    }
}
