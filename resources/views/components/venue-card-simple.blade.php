<div class="venueCard venueCard--simple">
    <div class="venueCard__top">
        <img class="venueCard__image" src="{{ $venue['image'] }}" alt="{{ $venue['name'] }}" />
        <div class="venueCard__top-content">
            <header class="venueCard__header">
                <h1 class="venueCard__title heading-md">{{ $venue['name'] }}</h1>
                <h2 class="venueCard__subtitle heading-sm">{{ $venue['location'] }}</h2>
                <div class="venueCard__types text-sm">
                    {{ implode(' • ', array_slice($venue['types'], 0, 2)) }}
                    @if(count($venue['types']) > 2)
                        <span>+{{ count($venue['types']) - 2 }} more</span>
                    @endif
                </div>
            </header>
        </div>
    </div>

    <div class="venueCard__footer">
        <div class="venueCard__price-container">
            <div class="venueCard__price">
                <span class="venueCard__price-from">from</span>
                <span class="venueCard__price-value">{{ $venue['price']['amount'] }} {{ $venue['price']['currency'] }}</span>
                <span class="venueCard__price-night">/ night</span>
            </div>
        </div>
    </div>
</div>
