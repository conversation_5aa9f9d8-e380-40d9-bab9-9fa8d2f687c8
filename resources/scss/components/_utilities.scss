@use '../settings/colors' as *;
@use '../settings/spacing' as *;
@use '../tools/mixins' as *;

.divider {
    display: flex;
    border: none;
    border-top: 2px solid $color-neutral-200;
}

// ==========================================================================
// Margin Top Utilities
// ==========================================================================

.mt-none { @include margin-top($spacing-none); }
.mt-xxs { @include margin-top($spacing-xxs); }
.mt-xs { @include margin-top($spacing-xs); }
.mt-sm { @include margin-top($spacing-sm); }
.mt-md { @include margin-top($spacing-md); }
.mt-lg { @include margin-top($spacing-lg); }
.mt-xl { @include margin-top($spacing-xl); }
.mt-2xl { @include margin-top($spacing-2xl); }
.mt-3xl { @include margin-top($spacing-3xl); }
.mt-4xl { @include margin-top($spacing-4xl); }
.mt-5xl { @include margin-top($spacing-5xl); }
.mt-6xl { @include margin-top($spacing-6xl); }
.mt-7xl { @include margin-top($spacing-7xl); }
.mt-8xl { @include margin-top($spacing-8xl); }

// ==========================================================================
// Margin Bottom Utilities
// ==========================================================================

.mb-none { @include margin-bottom($spacing-none); }
.mb-xxs { @include margin-bottom($spacing-xxs); }
.mb-xs { @include margin-bottom($spacing-xs); }
.mb-sm { @include margin-bottom($spacing-sm); }
.mb-md { @include margin-bottom($spacing-md); }
.mb-lg { @include margin-bottom($spacing-lg); }
.mb-xl { @include margin-bottom($spacing-xl); }
.mb-2xl { @include margin-bottom($spacing-2xl); }
.mb-3xl { @include margin-bottom($spacing-3xl); }
.mb-4xl { @include margin-bottom($spacing-4xl); }
.mb-5xl { @include margin-bottom($spacing-5xl); }
.mb-6xl { @include margin-bottom($spacing-6xl); }
.mb-7xl { @include margin-bottom($spacing-7xl); }
.mb-8xl { @include margin-bottom($spacing-8xl); }

// ==========================================================================
// Margin Left Utilities
// ==========================================================================

.ml-none { @include margin-left($spacing-none); }
.ml-xxs { @include margin-left($spacing-xxs); }
.ml-xs { @include margin-left($spacing-xs); }
.ml-sm { @include margin-left($spacing-sm); }
.ml-md { @include margin-left($spacing-md); }
.ml-lg { @include margin-left($spacing-lg); }
.ml-xl { @include margin-left($spacing-xl); }
.ml-2xl { @include margin-left($spacing-2xl); }
.ml-3xl { @include margin-left($spacing-3xl); }
.ml-4xl { @include margin-left($spacing-4xl); }
.ml-5xl { @include margin-left($spacing-5xl); }
.ml-6xl { @include margin-left($spacing-6xl); }
.ml-7xl { @include margin-left($spacing-7xl); }
.ml-8xl { @include margin-left($spacing-8xl); }

// ==========================================================================
// Margin Right Utilities
// ==========================================================================

.mr-none { @include margin-right($spacing-none); }
.mr-xxs { @include margin-right($spacing-xxs); }
.mr-xs { @include margin-right($spacing-xs); }
.mr-sm { @include margin-right($spacing-sm); }
.mr-md { @include margin-right($spacing-md); }
.mr-lg { @include margin-right($spacing-lg); }
.mr-xl { @include margin-right($spacing-xl); }
.mr-2xl { @include margin-right($spacing-2xl); }
.mr-3xl { @include margin-right($spacing-3xl); }
.mr-4xl { @include margin-right($spacing-4xl); }
.mr-5xl { @include margin-right($spacing-5xl); }
.mr-6xl { @include margin-right($spacing-6xl); }
.mr-7xl { @include margin-right($spacing-7xl); }
.mr-8xl { @include margin-right($spacing-8xl); }

// ==========================================================================
// Padding Top Utilities
// ==========================================================================

.pt-none { @include padding-top($spacing-none); }
.pt-xxs { @include padding-top($spacing-xxs); }
.pt-xs { @include padding-top($spacing-xs); }
.pt-sm { @include padding-top($spacing-sm); }
.pt-md { @include padding-top($spacing-md); }
.pt-lg { @include padding-top($spacing-lg); }
.pt-xl { @include padding-top($spacing-xl); }
.pt-2xl { @include padding-top($spacing-2xl); }
.pt-3xl { @include padding-top($spacing-3xl); }
.pt-4xl { @include padding-top($spacing-4xl); }
.pt-5xl { @include padding-top($spacing-5xl); }
.pt-6xl { @include padding-top($spacing-6xl); }
.pt-7xl { @include padding-top($spacing-7xl); }
.pt-8xl { @include padding-top($spacing-8xl); }

// ==========================================================================
// Padding Bottom Utilities
// ==========================================================================

.pb-none { @include padding-bottom($spacing-none); }
.pb-xxs { @include padding-bottom($spacing-xxs); }
.pb-xs { @include padding-bottom($spacing-xs); }
.pb-sm { @include padding-bottom($spacing-sm); }
.pb-md { @include padding-bottom($spacing-md); }
.pb-lg { @include padding-bottom($spacing-lg); }
.pb-xl { @include padding-bottom($spacing-xl); }
.pb-2xl { @include padding-bottom($spacing-2xl); }
.pb-3xl { @include padding-bottom($spacing-3xl); }
.pb-4xl { @include padding-bottom($spacing-4xl); }
.pb-5xl { @include padding-bottom($spacing-5xl); }
.pb-6xl { @include padding-bottom($spacing-6xl); }
.pb-7xl { @include padding-bottom($spacing-7xl); }
.pb-8xl { @include padding-bottom($spacing-8xl); }

// ==========================================================================
// Padding Left Utilities
// ==========================================================================

.pl-none { @include padding-left($spacing-none); }
.pl-xxs { @include padding-left($spacing-xxs); }
.pl-xs { @include padding-left($spacing-xs); }
.pl-sm { @include padding-left($spacing-sm); }
.pl-md { @include padding-left($spacing-md); }
.pl-lg { @include padding-left($spacing-lg); }
.pl-xl { @include padding-left($spacing-xl); }
.pl-2xl { @include padding-left($spacing-2xl); }
.pl-3xl { @include padding-left($spacing-3xl); }
.pl-4xl { @include padding-left($spacing-4xl); }
.pl-5xl { @include padding-left($spacing-5xl); }
.pl-6xl { @include padding-left($spacing-6xl); }
.pl-7xl { @include padding-left($spacing-7xl); }
.pl-8xl { @include padding-left($spacing-8xl); }

// ==========================================================================
// Padding Right Utilities
// ==========================================================================

.pr-none { @include padding-right($spacing-none); }
.pr-xxs { @include padding-right($spacing-xxs); }
.pr-xs { @include padding-right($spacing-xs); }
.pr-sm { @include padding-right($spacing-sm); }
.pr-md { @include padding-right($spacing-md); }
.pr-lg { @include padding-right($spacing-lg); }
.pr-xl { @include padding-right($spacing-xl); }
.pr-2xl { @include padding-right($spacing-2xl); }
.pr-3xl { @include padding-right($spacing-3xl); }
.pr-4xl { @include padding-right($spacing-4xl); }
.pr-5xl { @include padding-right($spacing-5xl); }
.pr-6xl { @include padding-right($spacing-6xl); }
.pr-7xl { @include padding-right($spacing-7xl); }
.pr-8xl { @include padding-right($spacing-8xl); }

