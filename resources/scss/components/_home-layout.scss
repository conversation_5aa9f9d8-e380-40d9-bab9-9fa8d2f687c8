.home-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xl;
    padding: $spacing-xl;
    height: calc(100vh - 72px); // înălțimea viewport minus header

    .venue-cards-section {
        overflow-y: auto;
        padding-right: $spacing-md;
    }

    .map-section {
        position: relative;
        height: 100%;
        
        iframe {
            width: 100%;
            height: 100%;
            border-radius: 12px;
        }

        .map-cards {
            position: absolute;
            top: $spacing-md;
            right: $spacing-md;
            width: 320px;
            max-height: calc(100% - $spacing-xl);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: $spacing-sm;
            padding-right: $spacing-sm;

            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-track {
                background: transparent;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba($color-leisure-700, 0.3);
                border-radius: 2px;
            }
        }
    }
}
