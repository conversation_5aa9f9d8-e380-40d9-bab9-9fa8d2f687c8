<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;

class BookingController extends Controller
{
    /**
     * Display the booking page.
     */
    public function index(Request $request): View
    {
        $locale = $request->route('locale', 'en');
        
        // Set the application locale
        app()->setLocale($locale);
        
        return view('booking.index', [
            'locale' => $locale,
            'bookingData' => $this->getBookingData(),
        ]);
    }
    
    /**
     * Get booking data for the page.
     * Mock data for the booking form.
     */
    private function getBookingData(): array
    {
        return [
            'hotel' => [
                'name' => 'Andaz Hotel, by Hyatt',
                'location' => 'Diplomatic Street 23, Zone 61, Doha',
                'image' => asset('assets/hotel-image.png'),
                'dates' => '30 Aug - 03 Sep'
            ],
            'rooms' => [
                [
                    'id' => 1,
                    'type' => '1 King Bed Deluxe',
                    'description' => 'Room only (non-refundable)',
                    'price' => 720,
                    'currency' => 'QAR',
                    'nights' => 1
                ],
                [
                    'id' => 2,
                    'type' => '1 King Bed Sea View',
                    'description' => 'Bed & breakfast (flexible rates)',
                    'price' => 1020,
                    'currency' => 'QAR',
                    'nights' => 2
                ]
            ],
            'total' => [
                'rooms_selected' => 3,
                'total_price' => 8280,
                'currency' => 'QAR',
                'nights' => 4
            ],
            'payment_methods' => [
                'google_pay',
                'apple_pay',
                'mastercard',
                'visa',
                'amex'
            ]
        ];
    }
}
