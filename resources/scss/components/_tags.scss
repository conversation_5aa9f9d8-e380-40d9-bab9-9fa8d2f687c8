@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/radius' as *;
@use '../settings/spacing' as *;
@use '../tools/mixins' as *;

.fgTags {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xxs;
  justify-content: flex-start;

  @include mq-md {
    justify-content: center;
  }
}

.fgTag {
  display: flex;
  justify-content: center;
  align-items: center;
  background: $color-brand-100;
  padding: $spacing-xxs;
  border-radius: $radius-md;
  line-height: 1;
}