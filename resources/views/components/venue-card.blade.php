<div class="venueCard">    <div class="venueCard__top">        <img class="venueCard__image" src="{{ $hotel['image'] }}" alt="{{ $hotel['name'] }}" />        <div class="venueCard__top-content">            <header class="venueCard__header">                <h1 class="venueCard__title heading-md">{{ $hotel['name'] }}</h1>                <h2 class="venueCard__subtitle heading-sm">{{ $hotel['location'] }}</h2>                <div class="venueCard__types text-sm">                    {{ $hotel['description'] }}                </div>            </header>            <div class="venueCard__badges">                <span class="venueCard__badge">                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none">                        <path d="M11.54 5.46c.*********** 0 .88l-4.19 4.19a.62.62 0 0 1-.88 0l-1.99-1.99a.62.62 0 1 1 .88-.88l1.55 1.55 3.75-3.75a.62.62 0 1 1 .88.88Z" fill="#2e8552" />                        <path d="M16 8a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-1.25 0A6.75 6.75 0 1 0 8 14.75 6.75 6.75 0 0 0 14.75 8Z" fill="#2e8552" />                    </svg>                    Free cancellation                </span>            </div>        </div>    </div>    <div class="venueCard__body">        <div class="venueCard__tags">            <span class="venueCard__tag">Popular for Business Travelers</span>            <span class="venueCard__tag">Long Stay Friendly</span>            <span class="venueCard__tag">Prime Location</span>        </div>        <hr class="venueCard__divider" />    </div>    <div class="venueCard__footer">        <div class="venueCard__price-container">            <div class="venueCard__price">                <span class="venueCard__price-from">from</span>                <span class="venueCard__price-value">{{ $hotel['price'] }} QAR</span>                <span class="venueCard__price-night">/ night</span>            </div>            <div class="venueCard__price-note">incl. taxes and fees</div>        </div>        <section class="venueCard__action">            <a href="#" class="fgBtn fgBtn--primary fgBtn--small">                View Rooms                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">                    <path d="M5.83337 14.1667L14.1667 5.83334" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>                    <path d="M5.83337 5.83334H14.1667V14.1667" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>                </svg>            </a>        </section>    </div></div>