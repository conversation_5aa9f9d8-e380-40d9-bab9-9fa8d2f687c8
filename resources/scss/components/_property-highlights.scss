@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/spacing' as *;
@use '../settings/radius' as *;
@use '../settings/breakpoints' as *;
@use '../tools/mixins' as *;

.propertyHighlightsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-lg;
  padding: $spacing-xl $spacing-lg;

  @include mq-lg {
    grid-template-columns: repeat(2, 1fr);
    row-gap: $spacing-lg;
    column-gap: $spacing-4xl;
    padding: $spacing-3xl;
  }
}

.propertyHighlights {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: $spacing-md;
  max-width: fit-content;

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: $spacing-4xl + $spacing-xs;
    height: $spacing-4xl + $spacing-xs;
    border-radius: 50%;
    background: $color-leisure-050;
    flex-shrink: 0;
  }

  &__icon-img {
    width: $spacing-lg;
    height: $spacing-lg;
    filter: brightness(0) saturate(100%) invert(12%) sepia(29%) saturate(3438%) hue-rotate(245deg) brightness(95%) contrast(96%);
  }

  &__body {
    flex: 1 1 auto;
    min-width: 3rem;

    &-title {
      @include text-styles(md);
      line-height: $line-height-none;
      font-weight: $font-weight-medium; 
      color: $color-black;
    }

    &-desc {
      @include text-styles(xs);
      color: $color-neutral-600;
      font-weight: $font-weight-normal;
    }
  }
} 