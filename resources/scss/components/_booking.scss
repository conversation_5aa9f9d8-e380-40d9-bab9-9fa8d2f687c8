@use '../settings/spacing' as *;
@use '../settings/breakpoints' as *;
@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../tools/mixins' as *;

.booking-container {
  padding: $spacing-lg;
}

.booking-layout {
  max-width: 1440px;
  background: white;
  display: flex;
  flex-direction: column;
  
  @include mq-lg {
    flex-direction: row;
  }
}

.booking-form-section {
  border: 1px solid $color-neutral-200;
  border-radius: $spacing-xs;
  flex: 1;
  padding: $spacing-xl;
  overflow-x: hidden;
  margin-right: 0;
  margin-bottom: $spacing-lg;

  @include mq-lg {
    padding: $spacing-2xl;
    margin-right: $spacing-lg;
    margin-bottom: 0;
  }
}

.booking-header {
  margin-bottom: $spacing-xl;
}

.booking-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $color-black;
  margin-bottom: $spacing-md;
}

.booking-auth-notice {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  font-size: $font-size-sm;
  
  strong {
    color: $color-leisure-500;
    font-weight: $font-weight-medium;
  }
}

.room-details {
  margin-bottom: $spacing-2xl;
  padding-bottom: $spacing-xl;
}

.room-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-extrabold;
  color: $color-black;
  margin-bottom: $spacing-xs;
}

.room-description {
  font-size: $font-size-sm;
  margin-bottom: $spacing-lg;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-md;
  margin-bottom: $spacing-md;

  @include mq-md {
    grid-template-columns: 1fr 1fr;
  }

  .fgInput {
    min-width: auto;
    width: 100%;

    .input {
      max-width: none;
      width: 100%;

      &:focus {
        border-color: $color-neutral-600;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      }

      &.valid {
        border-color: $color-success-500;
      }

      &--error {
        border-color: $color-danger-600;
      }
    }
  }
}

.input-icon {
  position: absolute;
  right: $spacing-md;
  top: 50%;
  transform: translateY(-50%);
  
  &.success {
    color: $color-success-500;
  }
}

.error-message {
  color: $color-danger-600;
  font-size: $font-size-xs;
  margin-top: $spacing-xs;
}

.info-sections {
  border-radius: $spacing-xs;
}

.info-section {
  background: $color-neutral-050;
  padding: $spacing-lg;

   &:nth-child(2),
   &:last-child {
    &::before {
      content: "";
      display: block;
      width: 100%; 
      margin-bottom: $spacing-2xl;
      border-top: 1px solid $color-neutral-100;
    }
  }
}

.info-header {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  
  svg {
    color: $color-neutral-500;
    flex-shrink: 0;
  }
  
  h3 {
    font-size: $font-size-base;
    font-weight: $font-weight-extrabold;
    color: $color-black;
    margin: 0;
  }
}

.info-section p {
  color: $color-neutral-800;
  font-size: $font-size-sm;
  line-height: 1.5;
  margin-bottom: $spacing-sm;
}

.payment-methods {
  display: flex;
  gap: $spacing-sm;
  flex-wrap: wrap;
}


.booking-summary-section {
  padding: $spacing-xl;
  height: fit-content;
  border: 1px solid $color-neutral-200;
  border-radius: $spacing-xs;
  
  @include mq-lg {
    width:  400px;
    padding: $spacing-2xl;
  }
}

.booking-summary {
  position: sticky;
  top: $spacing-lg;
}

.summary-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-extrabold;
  color: $color-black;
  margin-bottom: $spacing-lg;
}

.hotel-info {
  display: flex;
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-lg;
}

.hotel-image {
  width: 96px;
  height: 96px;
  border-radius: $spacing-xs;
  object-fit: cover;
  flex-shrink: 0;
}

.hotel-details {
  flex: 1;
}

.hotel-name {
  font-size: $font-size-base;
  font-weight: $font-weight-bold;
  color: $color-black;
  margin-bottom: $spacing-xs;
}

.hotel-location {
  color: $color-neutral-500;
  font-size: $font-size-sm;
  font-weight: $font-weight-normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;

   @include mq-lg {
    max-width: 230px;
    
  }
}

.hotel-dates {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $color-neutral-600;
  font-size: $font-size-sm;
  
  svg {
    flex-shrink: 0;
  }
}

.room-summary {
  margin-bottom: $spacing-lg;
  border-bottom: 1px solid $color-neutral-100;
  padding-bottom: $spacing-4xl;

}

.room-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: $spacing-md;
  
  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

.room-info {
  flex: 1;
  
  h4 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $color-black;
    margin-bottom: $spacing-xs;
  }
  
  p {
    color: $color-neutral-600;
    font-size: $font-size-xs;
  }
}

.room-price {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  
  > div {
    display: flex;
    gap: 4px; 
  }

  .nights {
    font-size: $font-size-sm;
    color: $color-neutral-600;
  }
  
  .price {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $color-black;
    margin: 0 $spacing-xs;
  }
  
  .per-night {
    font-size: $font-size-xs;
    color: $color-neutral-600;
  }
}

.price-summary {
  background: white;
  border-radius: $spacing-xs;
  margin-bottom: $spacing-lg;
}

.price-row {
  display: flex;
  color: $color-neutral-600;
  font-size: $font-size-xs;
  justify-content: space-between;
  align-items: center;
  
  &.total {
    margin-bottom: 0;
    color: $color-black;
    font-weight: $font-weight-extrabold;
    font-size: $font-size-lg;
  }
}

.total-price {
  font-size: $font-size-xl;
  font-weight: $font-weight-extrabold;
  color: $color-black;

  .currency {
    font-size: $font-size-base;
  }
}

.confirm-button {
  width: 100%;
  background: $color-leisure-700;
  color: white;
  border: none;
  padding: $spacing-sm;
  border-radius: $spacing-xs;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  cursor: pointer;
  
  &:hover {
    background: $color-leisure-700;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
  }
}
