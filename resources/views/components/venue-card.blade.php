<div class="venueCard">
    <!-- ── Top row (image + header) ── -->
    <div class="venueCard__top">
        <img class="venueCard__top-image" src="{{ $hotel['image'] }}" alt="{{ $hotel['name'] }}" />
        <div class="venueCard__top-content">
            <header class="venueCard__top-content-header">
                <h2 class="venueCard__top-content-title">{{ $hotel['name'] }}</h2>
                <h3 class="venueCard__top-content-subtitle">{{ $hotel['location'] }}</h3>
                <div class="venueCard__top-content-types ">@foreach($hotel['types'] as $index => $type)
                    {{ $type }}@if($index < count($hotel['types']) - 3) • @elseif($index===count($hotel['types']) - 3) • <span>+{{ count($hotel['types']) - $index - 1 }} more</span>@break @endif
                        @endforeach
                </div>
            </header>

            @if($hotel['badges'])
            <div class="venueCard__badges">
                @foreach($hotel['badges'] as $badge)
                <span class="venueCard__badge">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none">
                        <path
                            d="M11.54 5.46c.24.24.24.64 0 .88l-4.19 4.19a.62.62 0 0 1-.88 0l-1.99-1.99a.62.62 0 1 1 .88-.88l1.55 1.55 3.75-3.75a.62.62 0 1 1 .88.88Z"
                            fill="#2e8552" />
                        <path
                            d="M16 8a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-1.25 0A6.75 6.75 0 1 0 8 14.75 6.75 6.75 0 0 0 14.75 8Z"
                            fill="#2e8552" />
                    </svg>
                    {{ $badge['text'] }}
                </span>
                @endforeach
            </div>
            @endif
        </div>
    </div>

    <!-- ── Body (badges, tags, divider) ── -->
    <div class="venueCard__body">
        @if($hotel['tags'])
        <div class="venueCard__body-tags">
            @foreach($hotel['tags'] as $tag)
            <span class="fgTag">{{ $tag }}</span>
            @endforeach
        </div>
        @endif
        <hr class="divider" />
    </div>

    <div class="venueCard__footer">
        <div class="venueCardPriceContainer">
            <div class="venueCardPriceContainer__price">
                <span class="venueCardPriceContainer__price-from">from</span>
                <span class="venueCardPriceContainer__price-value">{{ $hotel['price']['amount'] }} {{ $hotel['price']['currency'] }}</span>
                <span class="venueCardPriceContainer__price-night">/ night</span>
            </div>
            <div class="venueCardPriceContainer__note">incl. taxes and fees</div>
        </div>
        <section class="venueCard__action">
            <a class="fgBtn fgBtn--primary fgBtn--small" href="{{route('hotel.details', ['locale' => app()->getLocale(), 'slug' => $hotel['slug']])}}">View Rooms
                <svg width="20" height="20" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.5 12.5L11.5 5.5M11.5 5.5H6.5M11.5 5.5V10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </a>
        </section>
    </div>
</div>
