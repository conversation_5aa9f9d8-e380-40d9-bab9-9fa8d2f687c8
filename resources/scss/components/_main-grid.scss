@use '../settings/spacing' as *;
@use '../settings/colors' as *;
@use '../settings/radius' as *;
@use '../settings/breakpoints' as *;
@use '../tools/mixins' as *;
@use '../settings/typography' as *;

.main-grid {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: $spacing-3xl 0;

  &__container {
    max-width: 100%;

    &-title {
      margin-bottom: $spacing-md;
      font-weight: $font-weight-bold !important;
      @include text-styles(md);
      color: $color-black;
    }

    &-layout {
      display: grid;
      grid-template-columns: 1fr;
      gap: $spacing-lg;
      align-items: start;

      &-content {}

      &-sidebar {
        background: $color-white;
        padding: $spacing-lg;
        border-radius: $radius-lg;
        border: 1px solid $color-neutral-200;
        min-height: 200px;
      }

      @include mq-md {
        grid-template-columns: 65% 30%;
        gap: $spacing-5xl;
      }
    }
  }
}