@use '../settings/colors' as *;
@use '../settings/spacing' as *;
@use '../settings/typography' as *;
@use '../settings/breakpoints' as *;
@use '../tools/mixins' as *;

.footer {
  background-color: $color-neutral-050;
  padding: $spacing-4xl $spacing-lg;
  color: $color-neutral-900;

  &__content {
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: $spacing-xl;

    &-description {
      display: flex;
      flex-direction: column;
      gap: $spacing-lg;

      &-logo {
        display: flex;
      }

      &-text {
        line-height: 160%;
        color: $color-neutral-700;
      }
    }

    &-links {
      display: flex;
      flex-direction: column;
      gap: $spacing-md;

      &-social {
        display: flex;
        gap: $spacing-md;
        flex-wrap: wrap;

        a {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          color: $color-neutral-900;
          text-decoration: none;

          &:hover {
            color: $color-leisure-700;
          }
        }
      }

      &-privacy {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        font-size: $font-size-sm;

        a {
          color: $color-neutral-800;
          text-decoration: none;

          &:hover {
            color: $color-leisure-700;
          }
        }

        span {
          color: $color-neutral-400;
        }
      }
    }

    &-copy {
      color: $color-neutral-600;
      line-height: 160%;
    }
  }

  @include mq-md {
    &__content {
      &-links {
        flex-direction: row;
        align-items: center;
      }
    }
  }

  @include mq-lg {
    padding: $spacing-5xl $spacing-4xl;
    &__content {
      gap: $spacing-3xl;
      &-description {
        flex-direction: row;
        align-items: flex-start;
        gap: $spacing-6xl;
      }
      &-links {
        gap: $spacing-4xl;
      }
    }
  }
}