@extends('layouts.app')

@section('title', __('Finish Your Booking') . ' - ' . config('app.name'))
@section('description', __('Complete your hotel booking with secure payment options.'))

@section('content')
<div class="booking-container">
    <div class="booking-layout">
        <div class="booking-form-section">
            <div class="booking-header">
                <h1 class="booking-title">Finish Your Booking</h1>
                <div class="booking-auth-notice">
                    <img src="{{ asset('images/svg/user_path.svg') }}" alt="FGREALTY user icon">
                    <span><strong>Sign in</strong> to book with your saved details or <strong>register</strong> to manage your bookings.</span>
                </div>
            </div>

            <div class="room-details">
                <h2 class="room-title">Room 1 details</h2>
                <p class="room-description">{{ $bookingData['rooms'][0]['type'] }} • {{ $bookingData['rooms'][0]['description'] }}</p>
                
                <div class="form-row">
                    <div class="fgInput">
                        <input type="text" class="input" value="Aisha Karim" placeholder="Full Name">
                        <div class="input-icon success">
                            <img src="{{ asset('images/svg/valid-check.svg') }}" alt="Valid check Icon">
                        </div>
                    </div>
                    <div class="fgInput">
                        <input type="email" class="input" value="<EMAIL>" placeholder="Email">
                    </div>
                </div>

                <div class="form-row">
                    <div class="fgInput">
                        <input type="tel" class="input" value="+974-5566-7788" placeholder="Phone Number">
                        <div class="input-icon success">
                            <img src="{{ asset('images/svg/valid-check.svg') }}" alt="Valid check Icon">
                        </div>
                    </div>
                    <div class="fgInput">
                        <select class="input">
                            <option>Nationality</option>
                            <option>Qatar</option>
                            <option>UAE</option>
                            <option>Saudi Arabia</option>
                        </select>
                    </div>
                </div>

                <textarea class="textarea" placeholder="Mentions or special requests"></textarea>
            </div>

            <div class="room-details">
                <h2 class="room-title">Room 2 details</h2>
                <p class="room-description">{{ $bookingData['rooms'][1]['type'] }} • {{ $bookingData['rooms'][1]['description'] }}</p>
                
                <div class="form-row">
                    <div class="fgInput">
                        <input type="text" class="input" value="James Whitmore" placeholder="Full Name">
                        <div class="input-icon success">
                            <img src="{{ asset('images/svg/valid-check.svg') }}" alt="Valid check Icon">
                        </div>
                    </div>
                    <div class="fgInput">
                        <input type="email" class="input input--error" value="james@.com" placeholder="Email">
                        <div class="error-message">Email address not entered correctly.</div>
                    </div>
                </div>

                <textarea class="textarea" placeholder="Mentions or special requests"></textarea>
            </div>

            <div class="info-sections">
                <div class="info-section">
                    <div class="info-header">
                        <img src="{{ asset('images/svg/info.svg') }}" alt="Info Icon">
                        <h3>Cancellation and prepayment</h3>
                    </div>
                    <p>Cancellation and prepayment policies vary according to accommodation type. Please check what conditions may apply to each option when making your selection.</p>
                    <p>Please note that the credit card holder must be present at check-in. Otherwise, an alternate credit card will be required.</p>
                </div>

                <div class="info-section">
                    <div class="info-header">
                        <img src="{{ asset('images/svg/payment-group.svg') }}" alt="Payment Methods">
                        <h3>Accepted payment methods</h3>
                    </div>
                    <div class="payment-methods">
                        <img src="{{ asset('images/svg/google-pay.svg') }}" alt="Google Pay">
                        <img src="{{ asset('images/svg/apple-pay.svg') }}" alt="Apple Pay">
                        <img src="{{ asset('images/svg/mastercard-apy.svg') }}" alt="Mastercard Pay">
                        <img src="{{ asset('images/svg/visa-pay.svg') }}" alt="Visa Pay">
                        <img src="{{ asset('images/svg/ae-pay.svg') }}" alt="Amex Pay">
                    </div>
                </div>

                <div class="info-section">
                    <div class="info-header">
                        <img src="{{ asset('images/svg/legal.svg') }}" alt="Legal Icon">
                        <h3>Legal information</h3>
                    </div>
                    <p>This property is managed, licensed or represented by a business.</p>
                </div>
            </div>
        </div>

        <div class="booking-summary-section">
            <div class="booking-summary">
                <h2 class="summary-title">Booking summary</h2>
                
                <div class="hotel-info">
                    <img src="{{ $bookingData['hotel']['image'] }}" alt="{{ $bookingData['hotel']['name'] }}" class="hotel-image">
                    <div class="hotel-details">
                        <h3 class="hotel-name">{{ $bookingData['hotel']['name'] }}</h3>
                        <p class="hotel-location">{{ $bookingData['hotel']['location'] }}</p>
                        <div class="hotel-dates">
                            <img src="{{ asset('images/svg/calendar.svg') }}" alt="Calendar Icon">
                            <span>{{ $bookingData['hotel']['dates'] }}</span>
                        </div>
                    </div>
                </div>

                <div class="room-summary">
                    @foreach($bookingData['rooms'] as $index => $room)
                    <div class="room-item">
                        <div class="room-info">
                            <h4>{{ $room['type'] }}</h4>
                            <p>{{ $room['description'] }}</p>
                        </div>
                        <div class="room-price">
                            <div>
                                <span class="nights">{{ $room['nights'] }}x</span>
                                <span class="price">{{ $room['price'] }} {{ $room['currency'] }}</span>
                            </div>
                            <span class="per-night">/ night</span>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="price-summary">
                    <div class="price-row">
                        <span>Price for {{ $bookingData['total']['nights'] }} nights</span>
                        <span>TOTAL</span>
                    </div>
                    <div class="price-row total">
                        <span>{{ $bookingData['total']['rooms_selected'] }} rooms selected</span>
                        <span class="total-price">
                            {{ $bookingData['total']['total_price'] }}
                            <span class="currency">{{ $bookingData['total']['currency'] }}</span>
                        </span>

                        <!-- <span class="total-price">{{ $bookingData['total']['total_price'] }} {{ $bookingData['total']['currency'] }}</span> -->
                    </div>
                </div>

                <button class="confirm-button">Confirm & Pay</button>
            </div>
        </div>
    </div>
</div>
@endsection
