@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/breakpoints' as *;
@use '../settings/radius' as *;
@use '../settings/spacing' as *;
@use '../tools/mixins' as *;

.venueCard {
  font-family: $font-family-primary;
  max-width: 100%;
  background: $color-white;
  border: 1px solid $color-neutral-200;
  border-radius: $radius-md;
  padding: $spacing-xs;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  &__top {
    display: flex;
    gap: $spacing-sm;

    &-image {
      min-width: 8.75rem;
      height: 11.75rem;
      border-radius: $radius-md;
      object-fit: cover;
    }

    &-content {
      display: flex;
      flex-direction: column;
      gap: $spacing-lg;
      padding: $spacing-xs $spacing-xs $spacing-xs 0;

      &-header {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      /* text styles */
      &-title {
        @include text-styles('xl');
        line-height: 1.1;
        font-weight: $font-weight-bold;

      }

      &-subtitle {
        @include text-styles('sm');
        color: $color-neutral-500;
        font-weight: $font-weight-normal;
        text-overflow: ellipsis;
        margin-top: $spacing-xs;
      }

      &-types {
        @include text-styles('sm');
        font-weight: $font-weight-extrabold;
        line-height: $line-height-tight;
        color: $color-black;
        margin-top: $spacing-md;

        span {
          font-weight: $font-weight-normal;
        }
      }
    }
  }

  /* body */
  &__body {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;

    &-tags {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xxs;
    }
  }

  &__badges {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
  }

  &__badge {
    display: flex;
    align-items: center;
    gap: $spacing-xxs;
    font-size: $font-size-sm;
    font-weight: $font-weight-normal;
    color: $color-success-600;
  }

  &__tag {
    background: $color-brand-100;
    padding: 0 $spacing-md;
    border-radius: $radius-md;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
    color: $color-neutral-900;
  }

  /* footer shown everywhere */
  &__footer {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: $spacing-md;

  }

  &__action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: $spacing-xs;
    padding-top: 0;

    .fgBtn--primary {
      width: 100%;

      &--small {
        width: 100%;
      }
    }

  }

  @include mq-md {
    display: grid;
    grid-template-columns: 16rem 1fr;
    gap: $spacing-md;

    &__top {
      display: contents;

      &-image {
        width: 15.625rem;
        height: 16rem;
        grid-column: 1;
        grid-row: 1 / 4;
      }

      &-content {
        padding-bottom: 0px;

        &-header {
          grid-column: 2;
          gap: 0;
        }

        &-subtitle {
          line-height: $line-height-none;
        }
      }
    }

    &__body {
      grid-column: 2;
    }

    &__footer {
      flex-direction: row;
    }

    &__action {
      width: 100%;
      max-width: 180px;
      align-self: stretch;
      justify-content: flex-end;
      padding: 0;
    }
  }
}

.venueCardPriceContainer {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: baseline;
  width: 100%;
  font-style: normal;
  padding: 0 $spacing-xs ;

  &__price {
    display: flex;
    flex-direction: row;
    gap: $spacing-xxs;
    justify-content: center;
    align-items: flex-end;

    &-from {
      @include text-styles('sm') {
        color: $color-black;
        font-weight: $font-weight-normal;
        line-height: $line-height-none;
      }
    }

    &-value {
      @include text-styles('xl');
      font-weight: $font-weight-bold;
      justify-content: center;
      line-height: $line-height-none;
    }

    &-night {
      @include text-styles('sm') {
        font-weight: $font-weight-bold;
        line-height: $line-height-none;
      }
    }
  }

  &__note {
    @include text-styles('xs') {
      font-weight: $font-weight-normal;
      line-height: 1;
      color: $color-neutral-500;
      text-align: start;
    }
  }

  @include mq-md {
    flex-direction: column;
    gap: $spacing-xs;
    padding: 0;

  }
}