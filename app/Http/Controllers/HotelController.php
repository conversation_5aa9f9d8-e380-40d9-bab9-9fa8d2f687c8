<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\Response;

class HotelController extends Controller
{
    /**
     * Display the specified hotel.
     */
    public function show(Request $request, string $slug): View
    {
        $locale = $request->route('locale', 'en');
        
        // Set the application locale
        app()->setLocale($locale);
        
        // Find hotel by slug
        $hotel = $this->findHotelBySlug($slug);
        
        if (!$hotel) {
            abort(404);
        }
        
        return view('hotels.show', [
            'locale' => $locale,
            'hotel' => $hotel,
            'relatedHotels' => $this->getRelatedHotels($hotel['id']),
        ]);
    }
    
    /**
     * Find hotel by slug.
     * In a real application, this would query the database.
     */
    private function findHotelBySlug(string $slug): ?array
    {
        $hotels = $this->getAllHotels();
        
        foreach ($hotels as $hotel) {
            if ($hotel['slug'] === $slug) {
                return $hotel;
            }
        }
        
        return null;
    }
    
    /**
     * Get all hotels.
     * In a real application, this would fetch from the database.
     */
    private function getAllHotels(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'Luxury Beach Resort',
                'slug' => 'luxury-beach-resort',
                'description' => 'Experience ultimate luxury at our beachfront resort with stunning ocean views and world-class amenities.',
                'long_description' => 'Nestled along the pristine coastline, our Luxury Beach Resort offers an unparalleled experience of comfort and elegance. With 200 beautifully appointed rooms and suites, each featuring private balconies with breathtaking ocean views, guests can immerse themselves in the tranquil beauty of the sea. Our resort boasts multiple dining options, including a fine-dining restaurant, casual beachside café, and poolside bar. Guests can enjoy our full-service spa, state-of-the-art fitness center, and a variety of water sports activities.',
                'price' => 299,
                'image' => 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop',
                'gallery' => [
                    'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&h=600&fit=crop',
                ],
                'rating' => 4.8,
                'location' => 'Dubai Marina',
                'amenities' => ['Free WiFi', 'Swimming Pool', 'Spa', 'Restaurant', 'Beach Access', 'Fitness Center', 'Room Service', 'Parking'],
                'rooms' => 200,
                'check_in' => '15:00',
                'check_out' => '12:00',
            ],
            [
                'id' => 2,
                'name' => 'Mountain View Hotel',
                'slug' => 'mountain-view-hotel',
                'description' => 'Escape to tranquility with breathtaking mountain views and premium amenities.',
                'long_description' => 'Perched high in the mountains, our Mountain View Hotel offers a serene retreat from the bustling city life. With 150 elegantly designed rooms, each offering panoramic mountain vistas, guests can reconnect with nature while enjoying modern comforts. The hotel features an award-winning restaurant specializing in local cuisine, a cozy lounge with fireplace, and extensive hiking trails. Our wellness center includes a spa, indoor pool, and yoga studio, perfect for relaxation and rejuvenation.',
                'price' => 199,
                'image' => 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=800&h=600&fit=crop',
                'gallery' => [
                    'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&h=600&fit=crop',
                ],
                'rating' => 4.6,
                'location' => 'Ras Al Khaimah',
                'amenities' => ['Free WiFi', 'Mountain Views', 'Spa', 'Restaurant', 'Hiking Trails', 'Indoor Pool', 'Yoga Studio', 'Fireplace Lounge'],
                'rooms' => 150,
                'check_in' => '14:00',
                'check_out' => '11:00',
            ],
            [
                'id' => 3,
                'name' => 'City Center Business Hotel',
                'slug' => 'city-center-business-hotel',
                'description' => 'Perfect for business travelers with modern facilities in the heart of the city.',
                'long_description' => 'Located in the heart of the business district, our City Center Business Hotel caters to the needs of modern business travelers. With 180 contemporary rooms and suites, each equipped with high-speed internet and ergonomic workspaces, guests can stay productive while enjoying comfort. The hotel features multiple meeting rooms, a business center, and a rooftop restaurant with city views. Our concierge service ensures seamless travel arrangements and local recommendations.',
                'price' => 149,
                'image' => 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=800&h=600&fit=crop',
                'gallery' => [
                    'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1596394516093-501ba68a0ba6?w=800&h=600&fit=crop',
                    'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&h=600&fit=crop',
                ],
                'rating' => 4.4,
                'location' => 'Downtown Dubai',
                'amenities' => ['Free WiFi', 'Business Center', 'Meeting Rooms', 'Restaurant', 'City Views', 'Concierge', 'Fitness Center', 'Rooftop Terrace'],
                'rooms' => 180,
                'check_in' => '15:00',
                'check_out' => '12:00',
            ],
        ];
    }
    
    /**
     * Get related hotels (excluding the current one).
     */
    private function getRelatedHotels(int $excludeId): array
    {
        $hotels = $this->getAllHotels();
        
        return array_filter($hotels, function ($hotel) use ($excludeId) {
            return $hotel['id'] !== $excludeId;
        });
    }
}
