@use '../settings/spacing' as *;
@use '../settings/breakpoints' as *;
@use '../settings/colors' as *;
@use '../tools/mixins' as *;

.searchPage {
  display: flex;
  flex-direction: column;
  padding-bottom: $spacing-5xl;

  @include mq-lg {
    flex-direction: row;
    justify-content: space-between;
  }
}

.hotelsList {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;

  &__summary {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
    padding: $spacing-xl $spacing-xs $spacing-xxs;
    color: $color-neutral-800;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.searchPageMapSection {
  display: none;

  @include mq-2lg {
    display: block;
    width: 35%;
    height: 801px;
    margin-right: 0;
    margin-left: $spacing-4xl;
    margin-top: 0;
  }
}