@extends('layouts.app')

@section('title', $hotel['name'] . ' - ' . config('app.name'))
@section('description', $hotel['description'])

@section('content')
<article class="detailsPage">
    <section class="detailsPage__topSection">
        <header class="detailsPage__topSection-header">
            <h1 class="detailsPage__topSection-header-title heading-sm">{{ $hotel['name'] }}</h1>
            <h2 class="detailsPage__topSection-header-subtitle text-md">{{ $hotel['address'] }}</h2>
            <section class="detailsPage__topSection-header-tags fgTags">
                @foreach($hotel['tags'] as $tag)
                <span class="fgTag">{{ $tag }}</span>
                @endforeach
            </section>
        </header>
        <div class="detailsPage__topSection-utilities">
            <div class="detailsPage__topSection-utilities-pricing">
                <span class="detailsPage__topSection-utilities-pricing-from">from</span>
                <span class="detailsPage__topSection-utilities-pricing-value heading-md">{{ $hotel['price']['amount']." ".$hotel['price']['currency'] }}</span>
                <span class="detailsPage__topSection-utilities-pricing-night">/ night</span>
            </div>
            <div class="detailsPage__topSection-utilities-note text-xs">incl. taxes and fees</div>
            <section class="detailsPage__topSection-utilities-ctas">
                <button class="detailsPage__topSection-utilities-ctas-share fgBtn fgBtn--secondary fgBtn--small fgBtn--icon fgBtn--left">
                    Share
                    <x-svg-icon name="share" />
                </button>
                <button class="detailsPage__topSection-utilities-ctas-save fgBtn fgBtn--secondary fgBtn--small fgBtn--icon fgBtn--left">
                    Save
                    <x-svg-icon name="save" />
                </button>
                <button class="detailsPage__topSection-utilities-ctas-book fgBtn fgBtn--primary fgBtn--small">
                    Book your stay
                </button>
            </section>
        </div>
    </section>

    <section class="detailsPage__gallery">
        <div class="gallery">
            <div class="gallery__item gallery--left">
                <img class="gallery__img" src="{{ asset($hotel['gallery'][0]) }}" alt="main">
            </div>
            <div class="gallery__item gallery--top">
                <img class="gallery__img" src="{{ asset($hotel['gallery'][1]) }}" alt="top">
            </div>
            <div class="gallery__item gallery--bottom-a">
                <img class="gallery__img" src="{{ asset($hotel['gallery'][2]) }}" alt="bottom">
            </div>
            <div class="gallery__item gallery--bottom-b">
                <img class="gallery__img" src="{{ asset($hotel['gallery'][3]) }}" alt="bottom">
                <span class="gallery--overlay"></span>
                <span class="gallery--caption">+{{ count($hotel['gallery']) }}&nbsp;Photos</span>
            </div>
        </div>
    </section>


    <section class="detailsPage__details">
        <x-main-grid title="Property Highlights">
            <x-slot:main>
                <x-grid :items="[
                [
                    'icon' => asset('images/svg/cocktail.svg'),
                    'title' => 'Free WiFi',
                    'description' => 'High-speed internet access throughout the property'
                ],
                [
                    'icon' => asset('images/svg/housekeeping.svg'),
                    'title' => 'Swimming Pool', 
                    'description' => 'Outdoor infinity pool with stunning city views'
                ],
                [
                    'icon' => asset('images/svg/Group.svg'),
                    'title' => 'Fitness Center',
                    'description' => '24/7 access to state-of-the-art equipment'
                ],
                [
                    'icon' => asset('images/svg/pool.svg'),
                    'title' => 'Spa & Wellness',
                    'description' => 'Full-service spa with relaxation treatments'
                ],
                [
                    'icon' => asset('images/svg/wellness.svg'),
                    'title' => 'Fine Dining',
                    'description' => 'Award-winning restaurants with international cuisine'
                ]
            ]" class="propertyHighlightsGrid" />
            </x-slot:main>

            <x-slot:sidebar>
                <!-- Sidebar content goes here -->
                <h3>Additional Information</h3>
                <p>This is the sidebar content area for additional information.</p>
            </x-slot:sidebar>
        </x-main-grid>
    </section>

</article>
@endsection