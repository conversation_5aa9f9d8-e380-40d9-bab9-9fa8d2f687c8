@use '../settings/breakpoints' as *;
@use '../settings/typography' as *;
@use '../settings/spacing' as *;
@use '../settings/colors' as *;
@use '../settings/headingsSizes' as *;
@use '../settings/text-sizes' as *;
@use 'sass:map';

// Media Queries 
@mixin mq-md {
  @media (min-width: $breakpoint-md) {
    @content;
  }
}

@mixin mq-sm {
  @media (min-width: $breakpoint-sm) {
    @content;
  }
}

@mixin mq-lg {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

@mixin mq-2lg {
  @media (min-width: $breakpoint-2lg) {
    @content;
  }
}

@mixin mq-xl {
  @media (min-width: $breakpoint-xl) {
    @content;
  }
}

// @TODO - check and remove if necessary
@mixin mq-tablet {
  @media (min-width: 481px) and (max-width: 768px) {
    @content;
  }
}

@mixin not-selectable {
  -webkit-touch-callout: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Safari */
  -khtml-user-select: none;
  /* Konqueror HTML */
  -moz-user-select: none;
  /* Old versions of Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  /* Non-prefixed version, currently
    supported by Chrome, Edge, Opera and Firefox */
    @content;
}

// Buttons
@mixin btn-disabled {
  @include not-selectable {
  cursor: not-allowed;
    pointer-events: none;
    @content;
  }
}

// Inner mixin for text styling - can be reused elsewhere
@mixin text-styles($size) {
  $token   : map.get(map.get($text-sizes,$size), var);
  $fallback: map.get(map.get($text-sizes,$size), fallback);

  // color      : $color-black;
  font-family: $font-family-primary;
  font-style : normal;
  font-weight: $font-weight-normal;
  font-size  : var(#{$token}, #{$fallback});
  line-height: 160%;

  &--normal {
    line-height: 100%;
  }

  @content;
}

@mixin text-class($size) {
  .text-#{$size} {
    @include text-styles($size) {
      @content;
    }
  }
}

// Inner mixin for heading styling - can be reused elsewhere
@mixin heading-styles($size) {
  $mobile  : map.get(map.get($heading-sizes, $size), mobile);
  $desktop : map.get(map.get($heading-sizes, $size), desktop);

  font-family: $font-family-primary;
  font-weight: $font-weight-medium;
  letter-spacing: 0;
  line-height: 1.1;
  color: $color-black;
  font-size: $mobile;

  @include mq-md { 
    font-size: $desktop; 
  }
  @content;
}

// Inner mixin for heading secondary styling with responsive support
@mixin heading-secondary-styles($size) {
  $mobile  : map.get(map.get($heading-sizes, $size), mobile);
  $desktop : map.get(map.get($heading-sizes, $size), desktop);

  font-family: $font-family-secondary;
  font-weight: $font-weight-semibold;
  text-transform: uppercase;
  letter-spacing: 0;
  line-height: 1.1;
  color: $color-black;
  font-size: $mobile;

  @include mq-md { 
    font-size: $desktop; 
  }
  @content;
}

// Headings
@mixin heading-class($size) {
  .heading-#{$size} {
    @include heading-styles($size) {
    @content;
    }
  }

  /* secondary modifier */
  .heading-#{$size}--secondary {
    @include heading-secondary-styles($size) {
      @content;
    }
  }
}
