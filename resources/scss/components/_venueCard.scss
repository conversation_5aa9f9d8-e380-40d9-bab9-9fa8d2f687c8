@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/breakpoints' as *;
@use '../settings/radius' as *;
@use '../settings/spacing' as *;
@use '../tools/mixins' as *;

.venueCard {
  font-family: $font-family-primary;
  max-width: 100%;
  background: $color-white;
  border: 1px solid $color-neutral-200;
  border-radius: $radius-md;
  padding: $spacing-xs;
  margin: $spacing-md;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;


  /* top section */
  &__top {
    display: flex;
    gap: $spacing-xl;

  }

  &__top-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  &__image {
    min-width: 8.75rem;
    height: 11.75rem;
    border-radius: $radius-md;
    object-fit: cover;

  }

  &__header {
    display: flex;
    flex-direction: column;
    justify-content: space-between;


  }

  /* text styles */
  &__title {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-xs;
  }

  &__subtitle {
    font-size: $font-size-sm;
    font-style: normal;
    color: $color-neutral-500;
    font-weight: $font-weight-normal;
    text-overflow: ellipsis;
    padding-bottom: $spacing-md;

  }

  &__types {
    font-size: $font-size-sm;
    font-weight: $font-weight-bold;
    line-height: $line-height-tight;
    color: $color-black;

    span {
      font-weight: $font-weight-normal;

    }
  }

  /* body */
  &__body {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;

  }

  &__badges {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;


  }

  &__badge {
    display: flex;
    align-items: center;
    gap: $spacing-xxs;
    font-size: $font-size-sm;
    font-weight: $font-weight-normal;
    color: $color-success-600;
  }

  &__tags {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-xxs;
  }

  &__tag {
    background: $color-brand-100;
    padding: 0 $spacing-md;
    border-radius: $radius-md;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;
    color: $color-neutral-900;
  }

  &__divider {
    border: none;
    border-top: 2px solid $color-neutral-200;

  }

  /* footer shown everywhere */
  &__footer {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: $spacing-md;

  }

  &__price-container {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: baseline;
    width: 100%;
    font-style: normal;

  }

  &__price {
    display: flex;
    flex-direction: row;
    gap: $spacing-xxs;
    justify-content: center;
    align-items: flex-end;


    &-from {
      font-size: $font-size-sm;
      color: $color-black;
      font-weight: $font-weight-semibold;
    }

    &-value {
      font-size: $font-size-xl;
      font-weight: $font-weight-semibold;
      justify-content: center;
    }

    &-night {
      font-size: $font-size-sm;
      font-weight: $font-weight-semibold;

    }

    &-note {
      font-size: $font-size-xs;
      font-weight: $font-weight-normal;
      color: $color-neutral-500;
      text-align: start;
    }

  }

  &__action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    .fgBtn--primary {
      width: 100%;

      &--small {
        width: 100%;
      }
    }

  }

  
   


  @include mq-md {

    display: grid;
    grid-template-columns: 16rem 1fr;
    gap: $spacing-md;

    &__top {
      display: contents;
    }


    &__image {
      max-width: 15.625rem;
      height: 100%;
      grid-column: 1;
      grid-row: 1 / 4;
    }

    &__header {
      grid-column: 2;
      gap: 0;
    }

    &__body {
      grid-column: 2;
    }

    &__footer {
      flex-direction: row;
    }

    &__price-container {
      flex-direction: column;
    }

    &__price {
      flex-direction: row;
      align-items: baseline;
      gap: $spacing-md;
    }

    &__action {
      width: 100%;
      max-width: 180px;
      align-self: stretch;
      justify-content: flex-end;

    }


    
   
  }
}