@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/breakpoints' as *;
@use '../settings/radius' as *;
@use '../settings/spacing' as *;
@use '../tools/mixins' as *;

.detailsPage {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
  margin-top: $spacing-4xl;

  &__topSection {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;

    &-header {
      display: flex;
      flex-direction: column;

      &-title {
        font-weight: $font-weight-bold;
      }

      &-subtitle {
        margin-top: $spacing-sm;
        line-height: $line-height-none;
      }

      &-tags {
        margin-top: $spacing-xl;
        justify-content: flex-start;
      }
    }

    &-utilities {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;

      &-pricing {
        display: flex;
        align-items: baseline;
        gap: $spacing-xs;
        font-size: $font-size-base;
        line-height: $line-height-none;

        &-from {
          font-weight: $font-weight-normal;
          font-size: $font-size-sm;
        }

        &-value {
          font-weight: $font-weight-bold;
          font-size: $font-size-lg;
        }

        &-night {
          font-weight: $font-weight-bold;
          font-size: $font-size-sm;
        }
      }

      &-note {
        color: $color-neutral-500;
        line-height: $line-height-none;
      }

      &-ctas {
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        gap: $spacing-xs;
        width: 100%;
        margin-top: $spacing-xs;

        &-book {
          flex: 1 1 auto;
          padding: $spacing-sm $spacing-2xl;
          font-size: $font-size-base;
        }

        &-share,
        &-save {
          flex: 0 0 3rem;
          height: 3rem;
          padding: 0 $spacing-md;
          font-size: 0;
          padding-right: $spacing-xs;
          display: inline-flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  @include mq-md {
    &__topSection {
      flex-direction: row;
      justify-content: space-between;
      gap: $spacing-md;

      &-utilities {
        justify-content: flex-end;

        &-pricing {
          justify-content: flex-end;

          &-value {
            font-size: $font-size-xl;
          }

          &-from,
          &-night {
            font-size: $font-size-sm;
          }
        }

        &-note {
          text-align: right;
        }

        &-ctas {
          display: flex;
          flex-direction: row;
          min-width: 100%;
          gap: $spacing-md;

          &-share,
          &-save {
            width: auto;
            padding: 0 $spacing-sm;
            font-size: $font-size-sm;
          }
        }
      }
    }
  }
}

.gallery {
  display: grid;
  width: 100%;
  gap: $spacing-xs;

  grid-template-columns: 2.1fr 1.05fr 1.05fr;
  grid-template-areas:
    "left top      top"
    "left top      top"
    "left bottom-a bottom-b";

  grid-auto-rows: 5.6rem;

  &__item {
    position: relative;
    overflow: hidden;
    border-radius: $radius-md;
  }

  &--left {
    grid-area: left;
  }

  &--top {
    grid-area: top;
    grid-row: span 2;
  }

  &--bottom-a {
    grid-area: bottom-a;
  }

  &--bottom-b {
    grid-area: bottom-b;
  }

  &__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: $radius-xs;
  }

  &--overlay {
    position: absolute;
    inset: 0;
    background: rgba(0 0 0 /.68);
  }

  &--caption {
    position: absolute;
    inset: 0;
    display: grid;
    place-items: center;
    font-weight: $font-weight-normal;
    color: $color-white;
  }

  @include mq-tablet {
    grid-auto-rows: 7rem;
  }

  @include mq-md {
    grid-auto-rows: 9rem;
  }

  @include mq-lg {
    grid-auto-rows: 11.3rem;
  }
}

.propertyHighlightsGrid {
  display: grid;
  grid-template-columns: 1fr;
  border: 1px solid $color-neutral-200;
  border-radius: $radius-lg ;
  gap: $spacing-lg;
  padding: $spacing-xl $spacing-lg;

  @include mq-md {
    grid-template-columns: repeat(2, 1fr);
    row-gap: $spacing-lg;
    column-gap: $spacing-4xl;
    padding: $spacing-3xl;
  }
}

.propertyHighlights {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: $spacing-md;
  max-width: fit-content;

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: $spacing-4xl + $spacing-xs;
    height: $spacing-4xl + $spacing-xs;
    border-radius: 50%;
    background: $color-leisure-050;
    flex-shrink: 0;
  }

  &__icon-img {
    width: $spacing-md;
    height: $spacing-md;

    filter: brightness(0) saturate(100%) invert(12%) sepia(29%) saturate(3438%) hue-rotate(245deg) brightness(95%) contrast(96%);
  }

  &__body {
    flex: 1 1 auto;
    min-width: 3rem;

    &-title {
      @include text-styles(sm);
      font-weight: $font-weight-bold;
      color: $color-black;
    }

    &-desc {
      @include text-styles(xs);
      color: $color-neutral-600;
    }
  }
}