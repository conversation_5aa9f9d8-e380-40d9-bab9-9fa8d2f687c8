<?php

namespace App\Http\Controllers;

use App\Services\HotelService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class HotelController extends Controller
{

    private $hotelService;
    public function __construct(HotelService $hotelService)
    {
        $this->hotelService = $hotelService;
    }

    public function search(Request $request, $locale): View
    {
        return view('hotels.search', [
            'locale' => $locale,
            'featuredHotels' => $this->hotelService->getHotels(),
        ]);
    }


    /**
     * Display the specified hotel.
     */
    public function details(Request $request, string $locale, string $slug): View
    {
        // Set the application locale
        app()->setLocale($locale);

        // Find hotel by slug
        $hotel = $this->findHotelBySlug($slug);

        if (!$hotel) {
            abort(404);
        }

        // dd($hotel);
        return view('hotels.details', [
            'locale' => $locale,
            'hotel' => $hotel,
            'relatedHotels' => $this->getRelatedHotels($hotel['id']),
        ]);
    }

    /**
     * Find hotel by slug.
     * In a real application, this would query the database.
     */
    private function findHotelBySlug(string $slug): ?array
    {
        $hotels = $this->hotelService->getHotels();

        foreach ($hotels as $hotel) {
            if ($hotel['slug'] === $slug) {
                return $hotel;
            }
        }

        return null;
    }

    /**
     * Get related hotels (excluding the current one).
     */
    private function getRelatedHotels(int $excludeId): array
    {
        $hotels = $this->hotelService->getHotels();

        return array_filter($hotels, function ($hotel) use ($excludeId) {
            return $hotel['id'] !== $excludeId;
        });
    }
}
