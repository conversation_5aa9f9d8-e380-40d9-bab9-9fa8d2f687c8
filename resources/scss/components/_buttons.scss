// ==========================================================================
// Components: Buttons
// ==========================================================================
@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/spacing' as *;
@use '../settings/radius' as *;
@use '../tools/mixins' as *;

.fgBtn {
    $blockName: &;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $font-size-base;
    line-height: 1.5;
    border-radius: $radius-md;
    padding: $spacing-sm $spacing-xl;
    border: none;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, border 0.2s;
    gap: $spacing-xs;
    color: $color-neutral-800;
    box-sizing: border-box;

    &--left {
        flex-direction: row-reverse;
    }

    &:disabled {
        @include btn-disabled;
    }

    &--small {
        padding: $spacing-sm $spacing-xl;
        line-height: 1.5rem;
        font-size: $font-size-sm;
    }

    &--big {
        padding: $spacing-md $spacing-xl;
        line-height: 1.5;
    }

    &--dark {
        background-color: $color-neutral-900;
        color: $color-white;

        &:hover:not(:disabled) {
            background-color: $color-neutral-800;
        }

        &:active:not(:disabled) {
            background-color: $color-black;
        }
        &:disabled {
            background-color: $color-neutral-200;
            color: $color-neutral-050;
        }
    }

    &--primary {
        background: $color-leisure-700;
        color: $color-white;

        &:hover:not(:disabled) {
            background: $color-leisure-600;
        }
        &:active:not(:disabled) {
            background: $color-leisure-800;
        }
        &:disabled {
            opacity: 0.25;
            background-color: $color-leisure-700;
        }
    }

    &--secondary {
        background: $color-white;
        color: $color-neutral-800;
        padding: calc($spacing-sm - 1px) $spacing-md;
        border: 1px solid $color-neutral-200;

        &:hover:not(:disabled) {
            color: $color-black;
            background: $color-white;
        }

        &:active:not(:disabled) {
            background: $color-neutral-100;
            border-color: $color-neutral-050;
        }

        &:disabled {
            background: $color-neutral-050;
            color: $color-neutral-300;
            border-color: $color-neutral-050;
        }
    }

    &--tertiary {
        background: $color-leisure-050;
        color: $color-leisure-700;
        padding: $spacing-sm $spacing-md;
        border: 1px solid $color-leisure-500;

        &:hover:not(:disabled) {
            background: $color-leisure-100;
        }
    }

    &--icon {

        img {
            width: $spacing-xl;
            height: $spacing-xl;
            object-fit: contain;
        }
    }
}