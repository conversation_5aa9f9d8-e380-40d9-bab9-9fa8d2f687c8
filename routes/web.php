<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HotelController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\PlaygroundController;

// Default route - redirect to English home
Route::get('/', function () {
    return redirect()->route('hotels.search', ['locale' => 'en']);
});

Route::get('/playground', [PlaygroundController::class, 'index'])->name('playground');


// Multilingual routes
Route::group(['prefix' => '{locale}', 'where' => ['locale' => 'en|ar']], function () {
    // Home routes
    // Route::get('/', [HomeController::class, 'index'])->name('home');
    
    Route::get('/', [HotelController::class, 'search'])->name('hotels.search');
    Route::get('/test', [BookingController::class, 'index'])->name('test');

    // Hotel routes
    Route::get('/{slug}', [HotelController::class, 'details'])
        ->where('slug', '[a-z0-9-]+')
        ->name('hotel.details');
});
