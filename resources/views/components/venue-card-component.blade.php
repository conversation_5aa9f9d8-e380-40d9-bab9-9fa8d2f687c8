<div class="venueCard">
    <!-- ── Top row (image + header) ── -->
    <div class="venueCard__top">
        <img class="venueCard__image" src="{{ $venue['image'] }}" alt="{{ $venue['name'] }}" />
        <div class="venueCard__top-content">
            <header class="venueCard__header">
                <h1 class="venueCard__title heading-md">{{ $venue['name'] }}</h1>
                <h2 class="venueCard__subtitle heading-sm">{{ $venue['location'] }}</h2>
                <div class="venueCard__types text-sm">
                    @foreach($venue['types'] as $index => $type)
                        {{ $type }}@if($index < count($venue['types']) - 3) • @elseif($index === count($venue['types']) - 3) • <span>+{{ count($venue['types']) - $index - 1 }} more</span>@break @endif
                    @endforeach
                </div>
            </header>

            @if($venue['badges'])
            <div class="venueCard__badges">
                @foreach($venue['badges'] as $badge)
                <span class="venueCard__badge">
                    <svg width="18" height="18" viewBox="0 0 16 16" fill="none">
                        <path
                            d="M11.54 5.46c.24.24.24.64 0 .88l-4.19 4.19a.62.62 0 0 1-.88 0l-1.99-1.99a.62.62 0 1 1 .88-.88l1.55 1.55 3.75-3.75a.62.62 0 1 1 .88.88Z"
                            fill="#2e8552" />
                        <path
                            d="M16 8a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-1.25 0A6.75 6.75 0 1 0 8 14.75 6.75 6.75 0 0 0 14.75 8Z"
                            fill="#2e8552" />
                    </svg>
                    {{ $badge['text'] }}
                </span>
                @endforeach
            </div>
            @endif
        </div>
    </div>

    <!-- ── Body (badges, tags, divider) ── -->
    <div class="venueCard__body">
        @if($venue['tags'])
        <div class="venueCard__tags">
            @foreach($venue['tags'] as $tag)
            <span class="venueCard__tag">{{ $tag }}</span>
            @endforeach
        </div>
        @endif

        <hr class="venueCard__divider" />
    </div>

    <div class="venueCard__footer">
        <div class="venueCard__price-container">
            <div class="venueCard__price">
                <span class="venueCard__price-from">from</span>
                <span class="venueCard__price-value">{{ $venue['price']['amount'] }} {{ $venue['price']['currency'] }}</span>
                <span class="venueCard__price-night">/ night</span>
            </div>
            <div class="venueCard__price-note">incl. taxes and fees</div>
        </div>
        <section class="venueCard__action">
            <button class="fgBtn fgBtn--primary fgBtn--small">
                View Rooms
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.5 12.5L11.5 5.5M11.5 5.5H6.5M11.5 5.5V10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </section>
    </div>
</div>
