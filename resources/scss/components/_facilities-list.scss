@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/spacing' as *;
@use '../tools/mixins' as *;

.facilitiesList {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  &__item {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    &-icon {
      width: $spacing-lg;
      height: $spacing-lg;
      aspect-ratio: 1/1;
      object-fit: contain;
      color: $color-leisure-600;
      filter: brightness(0) saturate(100%) invert(12%) sepia(29%) saturate(3438%) hue-rotate(245deg) brightness(95%) contrast(96%);
    }

    &-text {
      @include text-styles(sm);
      color: $color-leisure-900;
      font-weight: $font-weight-medium;
      line-height: $line-height-none;
    }
  }

  @include mq-md {
    flex-direction: row;
    flex-wrap: wrap;
  }
}