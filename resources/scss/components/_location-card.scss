@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../settings/spacing' as *;
@use '../settings/radius' as *;
@use '../tools/mixins' as *;

.locationCard {
  width: 100%;
  background: $color-white;
  border-radius: $radius-md;

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: $radius-lg $radius-lg 0 0;
  }

  &__content {
    padding: $spacing-xl;
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }

  &__title {
    @include text-styles(md);
    font-weight: $font-weight-normal;
    color: $color-black;
    line-height: $line-height-none;
  }

  &__link {
    @include text-styles(sm);
    color: $color-leisure-500;
    font-weight: $font-weight-normal;
    line-height: $line-height-none-01;
    margin-bottom: $spacing-sm;
  }

  &__item {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    &-icon {
      width: $spacing-lg;
      height: $spacing-lg;
      aspect-ratio: 1/1;
      object-fit: contain;
      color: $color-leisure-600;
    }

    &-text {
      @include text-styles(sm);
      color: $color-neutral-800;
      font-weight: $font-weight-normal;
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    &-distance {
      @include text-styles(sm);
      color: $color-neutral-800;
      font-weight: $font-weight-normal;
      line-height: $line-height-none;
    }
  }
} 