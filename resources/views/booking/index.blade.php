@extends('layouts.app')

@section('title', __('Finish Your Booking') . ' - ' . config('app.name'))
@section('description', __('Complete your hotel booking with secure payment options.'))

@section('content')
<div class="booking-container">
    <div class="booking-layout">
        <div class="booking-form-section">
            <div class="booking-header">
                <h1 class="booking-title">Finish Your Booking</h1>
                <div class="booking-auth-notice">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 7V5a3 3 0 0 0-6 0v2H1a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1H7V5a1 1 0 1 1 2 0v2h1a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h3V5a3 3 0 0 1 6 0v2h1a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1h-6a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1z" fill="currentColor"/>
                    </svg>
                    <span><strong>Sign in</strong> to book with your saved details or <strong>register</strong> to manage your bookings.</span>
                </div>
            </div>

            <div class="room-details">
                <h2 class="room-title">Room 1 details</h2>
                <p class="room-description">{{ $bookingData['rooms'][0]['type'] }} • {{ $bookingData['rooms'][0]['description'] }}</p>
                
                <div class="form-row">
                    <div class="fgInput">
                        <input type="text" class="input valid" value="Aisha Karim" placeholder="Full Name">
                        <div class="input-icon success">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                    <div class="fgInput">
                        <input type="email" class="input" value="<EMAIL>" placeholder="Email">
                    </div>
                </div>

                <div class="form-row">
                    <div class="fgInput">
                        <input type="tel" class="input valid" value="+974-5566-7788" placeholder="Phone Number">
                        <div class="input-icon success">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                    <div class="fgInput">
                        <select class="input">
                            <option>Nationality</option>
                            <option>Qatar</option>
                            <option>UAE</option>
                            <option>Saudi Arabia</option>
                        </select>
                    </div>
                </div>

                <div class="fgInput">
                    <textarea class="input" placeholder="Mentions or special requests"></textarea>
                </div>
            </div>

            <div class="room-details">
                <h2 class="room-title">Room 2 details</h2>
                <p class="room-description">{{ $bookingData['rooms'][1]['type'] }} • {{ $bookingData['rooms'][1]['description'] }}</p>
                
                <div class="form-row">
                    <div class="fgInput">
                        <input type="text" class="input valid" value="James Whitmore" placeholder="Full Name">
                        <div class="input-icon success">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M13.5 4.5L6 12L2.5 8.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                    <div class="fgInput">
                        <input type="email" class="input input--error" value="james@.com" placeholder="Email">
                        <div class="error-message">Email address not entered correctly.</div>
                    </div>
                </div>

                <div class="fgInput">
                    <textarea class="input" placeholder="Mentions or special requests"></textarea>
                </div>
            </div>

            <div class="info-sections">
                <div class="info-section">
                    <div class="info-header">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zM8 4a1 1 0 0 0-1 1v3a1 1 0 0 0 2 0V5a1 1 0 0 0-1-1zm0 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2z" fill="currentColor"/>
                        </svg>
                        <h3>Cancellation and prepayment</h3>
                    </div>
                    <p>Cancellation and prepayment policies vary according to accommodation type. Please check what conditions may apply to each option when making your selection.</p>
                    <p>Please note that the credit card holder must be present at check-in. Otherwise, an alternate credit card will be required.</p>
                </div>

                <div class="info-section">
                    <div class="info-header">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M2 4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4z" fill="currentColor"/>
                        </svg>
                        <h3>Accepted payment methods</h3>
                    </div>
                    <div class="payment-methods">
                        <div class="payment-icon google-pay">G Pay</div>
                        <div class="payment-icon apple-pay">Apple Pay</div>
                        <div class="payment-icon mastercard">MC</div>
                        <div class="payment-icon visa">VISA</div>
                        <div class="payment-icon amex">AMEX</div>
                    </div>
                </div>

                <div class="info-section">
                    <div class="info-header">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zM8 4a1 1 0 0 0-1 1v3a1 1 0 0 0 2 0V5a1 1 0 0 0-1-1zm0 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2z" fill="currentColor"/>
                        </svg>
                        <h3>Legal information</h3>
                    </div>
                    <p>This property is managed, licensed or represented by a business.</p>
                </div>
            </div>
        </div>

        <div class="booking-summary-section">
            <div class="booking-summary">
                <h2 class="summary-title">Booking summary</h2>
                
                <div class="hotel-info">
                    <img src="{{ $bookingData['hotel']['image'] }}" alt="{{ $bookingData['hotel']['name'] }}" class="hotel-image">
                    <div class="hotel-details">
                        <h3 class="hotel-name">{{ $bookingData['hotel']['name'] }}</h3>
                        <p class="hotel-location">{{ $bookingData['hotel']['location'] }}</p>
                        <div class="hotel-dates">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M12 2V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H3a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-1zM3 6h10v7H3V6z" fill="currentColor"/>
                            </svg>
                            <span>{{ $bookingData['hotel']['dates'] }}</span>
                        </div>
                    </div>
                </div>

                <div class="room-summary">
                    @foreach($bookingData['rooms'] as $index => $room)
                    <div class="room-item">
                        <div class="room-info">
                            <h4>{{ $room['type'] }}</h4>
                            <p>{{ $room['description'] }}</p>
                        </div>
                        <div class="room-price">
                            <span class="nights">{{ $room['nights'] }}x</span>
                            <span class="price">{{ $room['price'] }} {{ $room['currency'] }}</span>
                            <span class="per-night">/ night</span>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="price-summary">
                    <div class="price-row">
                        <span>Price for {{ $bookingData['total']['nights'] }} nights</span>
                        <span>TOTAL</span>
                    </div>
                    <div class="price-row total">
                        <span>{{ $bookingData['total']['rooms_selected'] }} rooms selected</span>
                        <span class="total-price">{{ $bookingData['total']['total_price'] }} {{ $bookingData['total']['currency'] }}</span>
                    </div>
                </div>

                <button class="confirm-button">Confirm & Pay</button>
            </div>
        </div>
    </div>
</div>
@endsection
