// ==========================================================================
// Settings: Spacing
// ==========================================================================

// Base spacing unit
$spacing-unit: 1rem; // 16px

// Spacing scale
$spacing-none: 0;        // 0px
$spacing-xxs:  0.25rem;  // 4px 
$spacing-xs: 0.5rem;     // 8px
$spacing-sm: 0.75rem;    // 12px
$spacing-md: $spacing-unit;   // 16px
$spacing-lg: 1.25rem;      // 20px
$spacing-xl: 1.5rem;    // 24px
$spacing-2xl: 1.75rem;  // 28px
$spacing-3xl: 2rem;     // 32px
$spacing-4xl: 2.5rem;   // 40px
$spacing-5xl: 3.75rem;  // 60px
$spacing-6xl: 5rem;     // 80px
$spacing-7xl: 7.5rem;   // 120px
$spacing-8xl: 10rem;    // 160px

// Container widths
$container-sm: 640px;
$container-md: 768px;
$container-lg: 1024px;
$container-xl: 1280px;
$container-2xl: 1536px;
