<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\HotelController;
use App\Http\Controllers\BookingController;

// Default route - redirect to English home
Route::get('/', function () {
    return redirect()->route('home', ['locale' => 'en']);
});

// Multilingual routes
Route::group(['prefix' => '{locale}', 'where' => ['locale' => 'en|ar']], function () {
    // Home routes
    Route::get('/', [HomeController::class, 'index'])->name('home');

    Route::get('/finish-booking', [BookingController::class, 'index'])->name('finish-booking');
    Route::get('/test', [BookingController::class, 'index'])->name('test');

    // Hotel routes
    Route::get('/{slug}', [HotelController::class, 'show'])
        ->where('slug', '[a-z0-9-]+')
        ->name('hotel.show');
});
