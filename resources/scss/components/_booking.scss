@use '../settings/spacing' as *;
@use '../settings/breakpoints' as *;
@use '../settings/colors' as *;
@use '../settings/typography' as *;
@use '../tools/mixins' as *;
@use '../settings/radius' as *;
.booking {
  padding: $spacing-lg;
  
  &__layout {
    max-width: 1440px;
    background: white;
    display: flex;
    flex-direction: column;
    
    @include mq-lg {
      flex-direction: row;
    }
  }
  
  &__form {
    border: 1px solid $color-neutral-200;
    border-radius: $spacing-xs;
    flex: 1;
    padding: $spacing-xl;
    overflow-x: hidden;
    margin-right: 0;
    margin-bottom: $spacing-lg;

    @include mq-lg {
      padding: $spacing-2xl;
      margin-right: $spacing-lg;
      margin-bottom: 0;
    }
  }
  
  &__header {
    margin-bottom: $spacing-xl;
  }
  
  &__title {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $color-black;
    margin-bottom: $spacing-md;
  }
  
  &__auth-notice {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    font-size: $font-size-sm;
  }
  
  &__auth-text {
    strong {
      color: $color-leisure-500;
      font-weight: $font-weight-medium;
    }
  }
  
  &__room {
    margin-bottom: $spacing-2xl;
    padding-bottom: $spacing-xl;
  }
  
  &__room-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-extrabold;
    color: $color-black;
    margin-bottom: $spacing-xs;
  }
  
  &__room-description {
    font-size: $font-size-sm;
    margin-bottom: $spacing-lg;
  }
  
  &__form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: $spacing-md;
    margin-bottom: $spacing-md;

    @include mq-md {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  .fgInput {
    min-width: auto;
    width: 100%;

    .input {
      max-width: none;
      width: 100%;
    }
  }
  &__input-group {
    min-width: auto;
    width: 100%;
  }
  
  &__input {
    width: 100%;
    max-width: none;

    &::placeholder {
      color: $color-neutral-800;
    }

    &:hover:not(:disabled):not(&--error):not(:focus) {
      border-color: $color-neutral-800;
    }

    &:focus {
      outline: none;
      border-color: $color-neutral-600;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }
    
    &--valid {
      border-color: $color-success-500;
    }
    
    &--error {
      border-color: $color-danger-600;
    }
  }
  
  &__input-icon {
    position: absolute;
    right: $spacing-md;
    top: 50%;
    transform: translateY(-50%);
    
    &--success {
      color: $color-success-500;
    }
  }
  
  &__error-message {
    color: $color-danger-600;
    font-size: $font-size-xs;
    margin-top: $spacing-xs;
  }
  
  &__info-sections {
    border-radius: $spacing-xs;
  }
  
  &__info-section {
    background: $color-neutral-050;
    padding: $spacing-lg;
   &:nth-child(2),
   &:last-child {
    &::before {
      content: "";
      display: block;
      width: 100%; 
      margin-bottom: $spacing-2xl;
      border-top: 1px solid $color-neutral-100;
    }
  }
  }
  
  &__info-header {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }
  
  &__info-icon {
    color: $color-neutral-500;
    flex-shrink: 0;
  }
  
  &__info-title {
    font-size: $font-size-base;
    font-weight: $font-weight-extrabold;
    color: $color-black;
    margin: 0;
  }
  
  &__info-text {
    color: $color-neutral-800;
    font-size: $font-size-sm;
    line-height: 1.5;
    margin-bottom: $spacing-sm;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  &__payment-methods {
    display: flex;
    gap: $spacing-sm;
    flex-wrap: wrap;
  }

  &__summary {
    border: 1px solid $color-neutral-200;
    border-radius: $spacing-xs;
    padding: $spacing-xl;
    height: fit-content;

    @include mq-lg {
      width:  400px;
      padding: $spacing-2xl;
    }
  }

  &__summary-content {
    position: sticky;
    top: $spacing-lg;
  }

  &__summary-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-extrabold;
    color: $color-black;
    margin-bottom: $spacing-lg;
  }

  &__hotel {
    display: flex;
    gap: $spacing-md;
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-lg;
  }

  &__hotel-image {
    width: 96px;
    height: 96px;
    border-radius: $spacing-xs;
    object-fit: cover;
    flex-shrink: 0;
  }

  &__hotel-details {
    flex: 1;
  }

  &__hotel-name {
    font-size: $font-size-base;
    font-weight: $font-weight-bold;
    color: $color-black;
    margin-bottom: $spacing-xs;
  }

  &__hotel-location {
    color: $color-neutral-500;
    font-size: $font-size-sm;
    font-weight: $font-weight-normal;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    @include mq-lg {
    max-width: 230px;
    } 
  }

  &__hotel-dates {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    color: $color-neutral-600;
    font-size: $font-size-sm;
  }

  &__hotel-calendar-icon {
    flex-shrink: 0;
  }

  &__hotel-dates-text {
    font-weight: $font-weight-bold;
    color: $color-neutral-800;
    font-size: $font-size-base;
  }

  &__rooms-summary {
    margin-bottom: $spacing-lg;
  border-bottom: 1px solid $color-neutral-100;
  padding-bottom: $spacing-4xl;  }

  &__room-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: $spacing-md;
    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }

  &__room-info {
    flex: 1;
  }

  &__room-type {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $color-black;
    margin-bottom: $spacing-xs;
  }
  &__room-desc {
    color: $color-neutral-600;
    font-size: $font-size-xs;
  }

  &__room-price {
    text-align: right;
    display: flex;
    flex-direction: column;
    align-items: flex-end;;
  }

  &__room-price-details {
    display: flex;
    gap: 4px; 
  }

  &__room-nights {
    font-size: $font-size-sm;
    color: $color-neutral-600;
  }

  &__room-amount {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $color-black;
    margin: 0 $spacing-xs;
  }

  &__room-per-night {
    font-size: $font-size-xs;
    color: $color-neutral-600;
  }

  &__price-summary {
    background: white;
    border-radius: $spacing-xs;
    margin-bottom: $spacing-lg;
  }

  &__price-row {
    display: flex;
    color: $color-neutral-600;
    font-size: $font-size-xs;
    justify-content: space-between;
    align-items: center;

    &--total {
      margin-bottom: 0;
      color: $color-black;
      font-weight: $font-weight-extrabold;
    }
  }

  &__price-label {
    font-size: $font-size-xs;
    color: $color-neutral-600;
  }

  &__price-total-label {
    font-size: $font-size-xs;
    color: $color-neutral-600;
  }

  &__price-rooms {
    font-size: $font-size-lg;
    color: $color-black;
  }

  &__price-total {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $color-black;
  }

  &__price-currency {
    font-size: $font-size-base;
    font-weight: $font-weight-extrabold;
  }

  &__confirm-button {
    width: 100%;
    background: $color-leisure-700;
    color: white;
    border: none;
    padding: $spacing-sm;
    border-radius: $spacing-xs;
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    cursor: pointer;

    &:hover {
      background: $color-leisure-700;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
    }
  }
}
