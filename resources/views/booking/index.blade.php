@extends('layouts.app')

@section('title', __('Finish Your Booking') . ' - ' . config('app.name'))
@section('description', __('Complete your hotel booking with secure payment options.'))

@section('content')
<div class="booking">
    <div class="booking__layout">
        <div class="booking__form">
            <div class="booking__header">
                <h1 class="booking__title">Finish Your Booking</h1>
                <div class="booking__auth-notice">
                    <img class="booking__auth-icon" src="{{ asset('images/svg/user_path.svg') }}" alt="FGREALTY user icon">
                    <span class="booking__auth-text"><strong>Sign in</strong> to book with your saved details or <strong>register</strong> to manage your bookings.</span>
                </div>
            </div>

            <div class="booking__room">
                <h2 class="booking__room-title">Room 1 details</h2>
                <p class="booking__room-description">{{ $bookingData['rooms'][0]['type'] }} • {{ $bookingData['rooms'][0]['description'] }}</p>
                <div class="booking__form-row">
                    <div class="booking__input-group fgInput">
                        <input type="text" class="input booking__input booking__input--valid" value="Aisha Karim" placeholder="Full Name">
                        <div class="booking__input-icon booking__input-icon--success">
                            <img src="{{ asset('images/svg/valid-check.svg') }}" alt="Valid check Icon">
                        </div>
                    </div>
                    <div class="booking__input-group fgInput">
                        <input type="email" class="input booking__input" value="<EMAIL>" placeholder="Email">
                    </div>
                </div>

                <div class="booking__form-row">
                    <div class="booking__input-group fgInput">
                        <input type="tel" class="input booking__input booking__input--valid" value="+974-5566-7788" placeholder="Phone Number">
                        <div class="booking__input-icon booking__input-icon--success">
                            <img src="{{ asset('images/svg/valid-check.svg') }}" alt="Valid check Icon">
                        </div>
                    </div>
                    <div class="fgInput booking__input-group">
                        <select class="input booking__input">
                            <option>Nationality</option>
                            <option>Qatar</option>
                            <option>UAE</option>
                            <option>Saudi Arabia</option>
                        </select>
                    </div>
                </div>

                <textarea class="textarea booking__input booking__input--textarea" placeholder="Mentions or special requests"></textarea>
            </div>

            <div class="booking__room">
                <h2 class="booking__room-title">Room 2 details</h2>
                <p class="booking__room-description">{{ $bookingData['rooms'][1]['type'] }} • {{ $bookingData['rooms'][1]['description'] }}</p>

                <div class="booking__form-row">
                    <div class="booking__input-group fgInput">
                        <input type="text" class="input booking__input booking__input--valid" value="James Whitmore" placeholder="Full Name">
                        <div class="booking__input-icon booking__input-icon--success">
                            <img src="{{ asset('images/svg/valid-check.svg') }}" alt="Valid check Icon">
                        </div>
                    </div>
                    <div class="booking__input-group fgInput">
                        <input type="email" class="input booking__input booking__input--error" value="james@.com" placeholder="Email">
                        <div class="booking__error-message">Email address not entered correctly.</div>
                    </div>
                </div>

                <textarea class="textarea booking__input booking__input--textarea" placeholder="Mentions or special requests"></textarea>
            </div>

            <div class="booking__info-sections">
                <div class="booking__info-section">
                    <div class="booking__info-header">
                        <img class="booking__info-icon" src="{{ asset('images/svg/info.svg') }}" alt="Info Icon">
                        <h3 class="booking__info-title">Cancellation and prepayment</h3>
                    </div>
                    <p class="booking__info-text">Cancellation and prepayment policies vary according to accommodation type. Please check what conditions may apply to each option when making your selection.</p>
                    <p class="booking__info-text">Please note that the credit card holder must be present at check-in. Otherwise, an alternate credit card will be required.</p>
                </div>

                <div class="booking__info-section">
                    <div class="booking__info-header">
                        <img class="booking__info-icon" src="{{ asset('images/svg/payment-group.svg') }}" alt="Payment Methods">
                        <h3 class="booking__info-title">Accepted payment methods</h3>
                    </div>
                    <div class="booking__payment-methods">
                        <img class="booking__payment-icon" src="{{ asset('images/svg/google-pay.svg') }}" alt="Google Pay">
                        <img class="booking__payment-icon" src="{{ asset('images/svg/apple-pay.svg') }}" alt="Apple Pay">
                        <img class="booking__payment-icon" src="{{ asset('images/svg/mastercard-apy.svg') }}" alt="Mastercard Pay">
                        <img class="booking__payment-icon" src="{{ asset('images/svg/visa-pay.svg') }}" alt="Visa Pay">
                        <img class="booking__payment-icon" src="{{ asset('images/svg/ae-pay.svg') }}" alt="Amex Pay">
                    </div>
                </div>

                <div class="booking__info-section">
                    <div class="booking__info-header">
                        <img class="booking__info-icon" src="{{ asset('images/svg/legal.svg') }}" alt="Legal Icon">
                        <h3 class="booking__info-title">Legal information</h3>
                    </div>
                    <p class="booking__info-text">This property is managed, licensed or represented by a business.</p>
                </div>
            </div>
        </div>

        <div class="booking__summary">
            <div class="booking__summary-content">
                <h2 class="booking__summary-title">Booking summary</h2>

                <div class="booking__hotel">
                    <img class="booking__hotel-image" src="{{ $bookingData['hotel']['image'] }}" alt="{{ $bookingData['hotel']['name'] }}">
                    <div class="booking__hotel-details">
                        <h3 class="booking__hotel-name">{{ $bookingData['hotel']['name'] }}</h3>
                        <p class="booking__hotel-location">{{ $bookingData['hotel']['location'] }}</p>
                        <div class="booking__hotel-dates">
                            <img class="booking__hotel-calendar-icon" src="{{ asset('images/svg/calendar.svg') }}" alt="Calendar Icon">
                            <span class="booking__hotel-dates-text">{{ $bookingData['hotel']['dates'] }}</span>
                        </div>
                    </div>
                </div>

                <div class="booking__rooms-summary">
                    @foreach($bookingData['rooms'] as $index => $room)
                    <div class="booking__room-item">
                        <div class="booking__room-info">
                            <h4 class="booking__room-type">{{ $room['type'] }}</h4>
                            <p class="booking__room-desc">{{ $room['description'] }}</p>
                        </div>
                        <div class="booking__room-price">
                            <div class="booking__room-price-details">
                                <span class="booking__room-nights">{{ $room['nights'] }}x</span>
                                <span class="booking__room-amount">{{ $room['price'] }} {{ $room['currency'] }}</span>
                            </div>
                            <span class="booking__room-per-night">/ night</span>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="booking__price-summary">
                    <div class="booking__price-row">
                        <span class="booking__price-label">Price for {{ $bookingData['total']['nights'] }} nights</span>
                        <span class="booking__price-total-label">TOTAL</span>
                    </div>
                    <div class="booking__price-row booking__price-row--total">
                        <span class="booking__price-rooms">{{ $bookingData['total']['rooms_selected'] }} rooms selected</span>
                        <span class="booking__price-total">
                            {{ $bookingData['total']['total_price'] }}
                            <span class="booking__price-currency">{{ $bookingData['total']['currency'] }}</span>
                        </span>
                    </div>
                </div>

                <button class="booking__confirm-button">Confirm & Pay</button>
            </div>
        </div>
    </div>
</div>
@endsection
